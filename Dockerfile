# Node modules stage.
FROM node:22.16-alpine AS base

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

ARG ENV_FILE='.env'

WORKDIR /app

COPY package.json .
COPY .npmrc .
COPY pnpm-lock.yaml .

RUN pnpm config set "strict-ssl" false -g
RUN pnpm install

# Build stage.
FROM base AS builder
WORKDIR /app
COPY . .
RUN npx nuxi build --dotenv $ENV_FILE

# Run stage
FROM node:22.16-alpine
WORKDIR /app
COPY --from=builder /app .

EXPOSE 3000

CMD [ "node", ".output/server/index.mjs" ]

