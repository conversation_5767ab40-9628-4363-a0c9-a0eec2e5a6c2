<template>
  <NuxtLayout :name="layout">
    <template #menu>
      <AstratexLogo />
      <MenuComponent />
      <FittingLink />
    </template>
    <template #content>
      <ArticleCardList :headline="true" />
    </template>
    <template #aside>
      <AvatarGroup />
      <FittingStoreLink />
    </template>
  </NuxtLayout>
</template>

<script setup lang="ts">
const layout = 'basic'
const { loading } = useLoadingStore()
definePageMeta({
  layout: false,
})
</script>

<style lang="sass">
@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins" as *

.logo-main
  text-align: center
  display: block
  min-height: 52px
  margin: 32px 0
  @include media-breakpoint-up(md)
    margin: 48px 0

  .el-image
    max-width: 200px

.article-logo
  border-top: 1px solid black
  padding: 48px 0
  margin: 0
</style>
