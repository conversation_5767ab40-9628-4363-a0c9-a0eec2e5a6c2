<template>
  <ContentCategory v-if="pageStore.currentPageType === PageType.Category && rewriterStore.rewriterData.view" />
  <ContentPost v-if="rewriterStore.rewriterData.view?.contentBlocks?.length > 0" />
</template>

<script lang="ts" setup>
import { PageType } from '~/core/model/base.model'

const { categories } = useRoute().params
const rewriterStore = useRewriterStore()

watch(() => categories, async () => {
  await rewriterStore.setRewriterData({ url: categories as string, query: {} })
  setPageType()
}, { immediate: true })

const pageStore = usePageStore()

const setPageType = () => {
  const pageType = rewriterStore.rewriterData.view?.contentBlocks === undefined || rewriterStore.rewriterData.view.contentBlocks?.length === 0 ? PageType.Category : PageType.Article
  pageStore.setCurrentPageType(pageType)
}
setPageType()
</script>
