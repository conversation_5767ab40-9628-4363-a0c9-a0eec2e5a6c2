import { SiteId } from '@atx-helper'

export const AXIOS_HEADERS = {
  common: {
    'Accept': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Credentials': 'true',
  },
}

export enum Pager {
  Take = 12,
  Skip = 0,
}

export enum HeadlineAlignType {
  Left = 1,
  Center = 2,
  Right = 3,
}

export enum TextType {
  Default = 1,
  Highlighted = 2,
}

export enum ParagraphType {
  Body = 1,
  BodySmall = 2,
  BodyLarge = 3,
  Caption = 4,
  CaptionCaps = 5,
}

/** Array of Site ids, on which the magazin fully enabled. Other sites have only landing-page enabled. */
export const FULLY_ENABLED_MAGAZINE_SITE_IDS: SiteId[] = [
  SiteId.CZ,
  SiteId.SK,
  SiteId.PL,
  SiteId.RO,
  SiteId.HU,
  SiteId.BG,
]

export const RULESET = {
  prg10: '20',
  prg11: '21',
  prg11b: '10',
  prg11s: '11',
  prg12: '01',
  prg18: '02',
  prg13: '00',
  prg120: '12',
  prg15: '03',
  prg16: '04',
  prg17: '05',
  mag11: 'm11',
  mag12: 'm12',
  mag13: 'm13',
  mag14: 'm14',
  mag15: 'm15',
  mag16: 'm16',
  mag17: 'm17',
  mag18: 'm18',
  mag19: 'm19',
  mag20: 'm20',
}
