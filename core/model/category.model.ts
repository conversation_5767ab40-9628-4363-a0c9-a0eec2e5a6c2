import { MetaViewModel } from './meta.model'
import type { PostBaseModel } from './post.model'

export class CategoryBaseModel {
  name: string = ''
  url: string = ''
  customCssClass: string = ''
  order: number = 0
}

export class CategoryListModel {
  id: number = 0
  name: string = ''
  order: number = 0
  isEnabled: boolean = false
  hasChildren: boolean = false
  children: [] = []
  parentId: number = 0
}

export class CategoryListModelTreeModel extends CategoryListModel {
  data = new CategoryTreeModel()
}

export class CategoryTreeModel extends CategoryBaseModel {
  id: number = 0
  children: [] = []
}

export class CategoryViewModel extends CategoryBaseModel {
  description: string = ''
  metaData = new MetaViewModel()
}

export class CategoryListModelListModelResponse {
  currentPage: number = 0
  perPage: number = 0
  totalCount: number = 0
  totalPages: number = 0
  items: PostBaseModel[] = []
}

export class CategoryModel extends CategoryBaseModel {
  id: number = 0
  description: string = ''
  isEnabled: boolean = false
  parentId: number = 0
  order: number = 0
  metaTitle: string = ''
  metaKeywords: string = ''
  metaDescription: string = ''
}
