export interface ErrorResponseBody {
  title: string
  statusCode: number
  detail: string
}

export interface IPrivateRuntimeConfig {
  apiKey: string
  apiSecret: string
}

export interface IPublicRuntimeConfig {
  nodeEnv: string
  imageUrl: string
  apiUrl: string
  magazineApi: string
  imageServerUrl: string
  nuxtBranch: 'local' | 'dev' | 'prod' | 'release'
}

export type IRuntimeConfig = IPrivateRuntimeConfig & IPublicRuntimeConfig

export enum PageType {
  HomePage = 'hp',
  Article = 'article',
  Category = 'category',
}

export interface MagazineCommodity {
  id: number
  code: string
  color: string
  image: string
  url: string
  name: string
  annotation: string
  isPriceRange: boolean
  priceVat: string
  priceMaxVat: string
  imageInfo: ImageInfo
}

export class ImageInfo {
  image: string = ''
  isOnCDN: boolean = true
  hasRuleset: boolean = true
}
