import type { CategoryListModelListModelResponse } from './category.model'
import type { MetaViewModel } from './meta.model'
import type { PostViewModel } from './post.model'

export interface RewriterModel {
  categoryId: number
  postId: number
  tagId: number
  authorId: number
  pageId: number
  metaInfo: MetaViewModel
}
export interface IRewriterView extends PostViewModel {
  customCssClass: string
  description: string
  layoutType: string
}
export interface RewriterDataModel {
  view: IRewriterView
  data: CategoryListModelListModelResponse
}

export type QueryDictionary = { [key: string]: string | number | (string | null)[] }

export class RewriterRequest {
  url: string
  query: QueryDictionary

  constructor(url: string, query: QueryDictionary) {
    this.url = url
    this.query = query
  }
}

export class RewriterError {
  code: number
  message: string
  request: RewriterRequest
  constructor(code: number, message: string, request: RewriterRequest) {
    this.code = code
    this.message = message
    this.request = request
  }
}
