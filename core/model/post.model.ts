import { AuthorBaseModel } from './author.model'
import type { CategoryBaseModel } from './category.model'
import type { MetaViewModel } from './meta.model'
import type { TagBaseModel } from './tag.model'
import type * as block from './content.model'

type ContentBlock =
  | block.BaseContentBlockModel
  | block.HeadlineContentBlockModel
  | block.ImageContentBlockModel
  | block.ProductsByCodeContentBlockModel
  | block.TextContentBlockModel
  | block.TipContentBlockModel
  | block.VideoContentBlockModel

export enum SourceType {
  ABSOLUTE__URL = 'AbsoluteUrl',
  RELATIVE__URL = 'RelativeUrl',
}

export class ImageData {
  image: string = ''
  isOnCdn: boolean = false
  hasRuleset: boolean = false
  source: SourceType = SourceType.RELATIVE__URL
};

export class PostBaseModel {
  id: number = 0
  mainImage: ImageData = new ImageData()
  mainImageUrl: string = ''
  mainImageAuthor: string = ''
  featureImage: ImageData = new ImageData()
  featureImageUrl: string = ''
  name: string = ''
  url: string = ''
  perex: string = ''
  date: Date = new Date()
  categories: Array<CategoryBaseModel> = []
  author = new AuthorBaseModel()
  layoutType: string = ''
  hasBreadcrumbs: boolean = false
  hasPostAuthor: boolean = false
}

export class PostBaseModelListModelResponse {
  currentPage: number = 0
  perPage: number = 0
  totalCount: number = 0
  totalPages: number = 0
  items: Array<PostBaseModel> = []
}

export interface PostViewModel extends PostBaseModel {
  metaData: MetaViewModel
  tags: TagBaseModel[]
  contentBlocks: ContentBlock[]
  postMainImage: block.VideoImageBlockModel
}
