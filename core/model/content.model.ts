import type { MagazineCommodity } from '~/core/model/commodity.model'

enum ProductsByCodePresentationType {
  Carousel,
  List,
}

export interface contentLinkModel {
  title: string
  url: string
  hasLink: boolean
  text: string
  hoverLinkColor: string
  hoverLinkTextColor: string
  linkColor: string
  linkTextColor: string
}

export interface BaseContentBlockModel {
  id: number
  type: string
  order: number
  htmlId: string
}

export interface WrapperBlockModel<TBlock> {
  blocks: TBlock[]
}

export interface HeadlineContentBlockModel extends BaseContentBlockModel {
  headlineType: number
  headlineAlignType: number
  text: string
}

export interface ImageContentBlockModel extends BaseContentBlockModel {
  imageUrl: string
  linkUrl: string
  altText: string
  title: string
  imageCarouselId: number
}

export interface ImageCarouselModel extends BaseContentBlockModel {
  images: ImageContentBlockModel[]
  link: contentLinkModel
}

export interface ProductsByCodeContentBlockModel extends BaseContentBlockModel {
  title: string
  link: contentLinkModel
  count: number
  products: string
  hasLink: boolean
  presentationType: ProductsByCodePresentationType
  data: MagazineCommodity[]
}

export interface TextContentBlockModel extends BaseContentBlockModel {
  text: string
}

export interface VideoContentBlockModel extends BaseContentBlockModel {
  videoUrl: string
  imageUrl: string
  autoPlay: boolean
  controls: boolean
  loop: boolean
  muted: boolean
  width: number
  height: number
  description: string
}

export interface TipContentBlockModel extends BaseContentBlockModel {
  text: string
  title: string
  hasVideo: boolean
  hasImage: boolean
  imageData: ImageContentBlockModel
  videoData: VideoContentBlockModel
}

export interface PersonInfoBlockModel extends BaseContentBlockModel {
  imageUrl: string
  name: string
  jobPosition: string
  description: string
}

export interface ImageWithInfoBlockModel extends BaseContentBlockModel {
  image1Url: string
  image1Alt: string
  image1Title: string
  image1Text: string
  image1LinkUrl: string

  image2Url: string
  image2Alt: string
  image2Title: string
  image2Text: string
  image2LinkUrl: string

  image3Url: string
  image3Alt: string
  image3Title: string
  image3Text: string
  image3LinkUrl: string
}

export interface ProductCardBlockModel extends BaseContentBlockModel {
  altText: string
  imageUrl: string
  link: contentLinkModel
  text: string
  title: string
  color: string
  textColor: string
}

export interface ProductCardWrapperBlockModel extends BaseContentBlockModel, WrapperBlockModel<ProductCardBlockModel> { }

export interface ProductCollectionBlockModel extends BaseContentBlockModel {
  color: string
  textColor: string
  icon: string
  imageUrl: string
  links: contentLinkModel[]
  products: string
  productCodes: string[]
  text: string
  title: string
  data: MagazineCommodity[]
}

export interface VideoImageBlockModel extends BaseContentBlockModel {
  imageLeft: ImageContentBlockModel
  imageRight: ImageContentBlockModel
  mobileImageLeft: ImageContentBlockModel
  video: VideoContentBlockModel
  linkLeft: string
  linkRight: string
  linkText: string
}

export interface PromoVoucherBoxModel {
  icon: string
  link: contentLinkModel
  text: string
  title: string
}
export interface PromoVoucherBlockModel extends BaseContentBlockModel {
  text: string
  title: string
  color: string
  textColor: string
  promoVoucherBoxes: PromoVoucherBoxModel[]
}
