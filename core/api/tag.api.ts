import { PostBaseModelListModelResponse } from '../model/post.model'
import type { TagViewModel } from '../model/tag.model'

const getTagById = async (id: number) => {
  try {
    return await useFetch<TagViewModel>(`/rest/public-content/tag/${id}`)
  }
  catch {
    return {} as TagViewModel
  }
}

const getPostsByTag = async (id: number) => {
  try {
    return await useFetch<PostBaseModelListModelResponse>(`/rest/public-content/tag/${id}/posts`)
  }
  catch {
    return new PostBaseModelListModelResponse()
  }
}

export const tagApi = {
  getTagById,
  getPostsByTag,
}
