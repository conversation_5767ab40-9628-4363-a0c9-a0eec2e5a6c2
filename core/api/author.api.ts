import type { AuthorViewModel } from '../model/author.model'
import { PostBaseModelListModelResponse } from '../model/post.model'

const getAuthors = async () => {
  try {
    return (await useFetch<AuthorViewModel[]>(`${useRuntimeConfig().public.magazineURL}/rest/public-content/author/?hasPublicPosts=true`)).data.value ?? []
  }
  catch {
    return [] as AuthorViewModel[]
  }
}

const getAuthorById = async (id: number) => {
  try {
    return await useFetch<PostBaseModelListModelResponse>(`/rest/public-content/author/${id}/posts`)
  }
  catch {
    return new PostBaseModelListModelResponse()
  }
}

export const authorApi = {
  getAuthors,
  getAuthorById,
}
