import type { PostBaseModel, PostViewModel } from '../model/post.model'

const getPostById = async (id: number) => {
  try {
    return await useFetch<PostViewModel>(`/rest/public-content/post/${id}`)
  }
  catch {
    return {} as PostViewModel
  }
}

const getRelatedByPost = async (id: number) => {
  try {
    return await useFetch<PostBaseModel[]>(`/rest/public-content/post/${id}/related`)
  }
  catch {
    return []
  }
}
export const postApi = {
  getPostById,
  getRelatedByPost,
}
