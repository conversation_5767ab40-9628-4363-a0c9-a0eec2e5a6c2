import type { AuthorBaseModel } from '../model/author.model'
import { CategoryViewModel, type CategoryTreeModel } from '../model/category.model'
import { PostBaseModelListModelResponse } from '../model/post.model'

const getCategoryTree = async () => {
  try {
    return (await useFetch<CategoryTreeModel[]>(
      `${
        useRuntimeConfig().public.magazineURL
      }/rest/public-content/category/tree?OrderBy=order`,
      {
        method: 'GET',
      },
    )).data.value ?? []
  }
  catch {
    return []
  }
}

const getCategoryById = async (id: number) => {
  try {
    return await useFetch<CategoryViewModel>(`/rest/public-content/category/${id}`)
  }
  catch {
    return new CategoryViewModel()
  }
}

const getPostsByCategory = async (id: number) => {
  try {
    return await useFetch<PostBaseModelListModelResponse>(`/rest/public-content/category/${id}/posts`)
  }
  catch {
    return new PostBaseModelListModelResponse()
  }
}

const getAuthorsByCategory = async (id: number) => {
  try {
    return await useFetch<AuthorBaseModel[]>(`/rest/public-content/category/${id}/authors`)
  }
  catch {
    return [] as AuthorBaseModel[]
  }
}

export const categoryApi = {
  getCategoryTree,
  getCategoryById,
  getPostsByCategory,
  getAuthorsByCategory,
}
