import type { RewriterDataModel, RewriterRequest } from '../model/rewrite.model'

function getQueryString(request: RewriterRequest) {
  const pairs: { key: string, value: string | number }[] = []

  if (request.url && request.url.length > 0) {
    pairs.push({ key: 'url', value: request.url })
  }

  for (const key of Object.keys(request.query)) {
    const value: string | number | (string | null)[] = request.query[key]
    if ((typeof value === 'string' && value.length > 0) || typeof value === 'number') {
      pairs.push({ key, value })
    }
    else if (Array.isArray(value) && value.length > 0 && value[0] !== null) {
      pairs.push(...(<string[]>value).map(v => ({ key, value: v })))
    }
  }

  if (pairs.length <= 0) {
    return ''
  }

  return `?${pairs.map(p => `${p.key}=${p.value}`).join('&')}`
}

const getUrlData = async (request: RewriterRequest): Promise<RewriterDataModel> => {
  const queryString = getQueryString(request)
  try {
    return (await useFetch<RewriterDataModel>(`${useRuntimeConfig().public.magazineURL}/rest/rewriter${queryString}`)).data.value ?? {} as RewriterDataModel
  }
  catch (e) {
    return e as unknown as RewriterDataModel
  }
}

const getRewriterData = async (request?: RewriterRequest): Promise<RewriterDataModel> => {
  const queryString = request ? getQueryString(request) : ''
  try {
    return (await useFetch<RewriterDataModel>(`${useRuntimeConfig().public.magazineURL}/rest/rewriter/data${queryString}`)).data.value ?? {} as RewriterDataModel
  }
  catch (e) {
    return e as unknown as RewriterDataModel
  }
}

export const rewriterApi = {
  getUrlData,
  getRewriterData,
}
