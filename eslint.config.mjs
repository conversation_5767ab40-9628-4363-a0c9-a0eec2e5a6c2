// @ts-check
import { createConfigForNuxt } from '@nuxt/eslint-config/flat'

// Run `npx @eslint/config-inspector` to inspect the resolved config interactively
export default createConfigForNuxt({
  features: {
    // Rules for module authors
    tooling: true,
    // Rules for formatting
    stylistic: true,
  },
})
  .override('nuxt/typescript/rules', {
    rules: {
      '@typescript-eslint/no-explicit-any': 'warn',
    },
  })
  .override('nuxt/vue/rules', {
    rules: {
      'vue/no-v-html': 'off',
      'vue/max-attributes-per-line': [
        'error',
        {
          singleline: 10,
          multiline: 10,
        },
      ],
      'vue/html-self-closing': [
        'error',
        {
          html: {
            void: 'any',
            normal: 'any',
            component: 'any',
          },
          svg: 'any',
          math: 'any',
        },
      ],
    },
  })

  .override('nuxt/tooling/regexp', {
    rules: {
      'no-control-regex': ['warn'],
      'regexp/no-unused-capturing-group': ['warn'],
      'regexp/no-super-linear-backtracking': ['warn'],
      'regexp/no-misleading-capturing-group': ['warn'],
      'regexp/optimal-quantifier-concatenation': ['warn'],
    },
  })
