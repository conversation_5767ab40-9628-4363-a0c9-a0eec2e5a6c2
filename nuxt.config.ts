// https://nuxt.com/docs/api/configuration/nuxt-config
import { dirname, resolve } from 'node:path'
import { fileURLToPath } from 'node:url'
import { i18nlocales } from './node_modules/@atx-eshop/eshop-components/dist/runtime/helpers/i18n.locales'

const __dirname = dirname(fileURLToPath(import.meta.url))
const getDynamicLocales = () =>
  i18nlocales.map(l => ({ ...l, file: 'dynamicTranslation.ts' }))
export default defineNuxtConfig({
  modules: ['@nuxtjs/i18n', '@pinia/nuxt', '@element-plus/nuxt', '@atx-eshop/eshop-components', '@nuxt/eslint', 'vue3-carousel-nuxt'],

  plugins: ['@/plugins/01.init.server', '@/plugins/rewriterApi'],

  devtools: { enabled: true },

  app: {
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      title: 'Magazín | Astratex.cz',
      meta: [{ name: 'description', content: '' }],
      link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }],
    },
  },

  runtimeConfig: {
    apiKey: process.env.NUXT_API_KEY,
    apiSecret: process.env.NUXT_API_SECRET,

    public: {
      nuxtBranch: process.env.NUXT_NUXT_BRANCH,
      baseURL: process.env.NUXT_PUBLIC_BASE_URL,
      magazineURL: process.env.NUXT_PUBLIC_MAGAZINE_URL,
      loginTarget: process.env.NUXT_PUBLIC_LOGIN_TARGET,
      googleMapsApiKey: process.env.NUXT_PUBLIC_GOOGLE_MAPS_API_KEY,
      paczkomatWidgetToken: process.env.NUXT_PUBLIC_PACKOMAT_WIDGET_TOKEN,
      imageUrl: process.env.NUXT_PUBLIC_IMAGE_URL,
      cdn: process.env.NUXT_PUBLIC_CDN,
      azureBlobUrl: process.env.NUXT_PUBLIC_AZURE_BLOB_URL,
      translationBaseUrl: process.env.NUXT_PUBLIC_TRANSLATION_BASE_URL,
      gatewayKey: process.env.NUXT_PUBLIC_GATEWAY_KEY,
      applicationType: process.env.NUXT_PUBLIC_APLICATION_TYPE,
      exchangePath: process.env.NUXT_PUBLIC_EXCHANGE_PATH,
      eshopPath: process.env.NUXT_PUBLIC_ESHOP_PATH,
      webApiUrl: process.env.NUXT_PUBLIC_WEBAPI_URL,
      useApiGw: process.env.NUXT_PUBLIC_USE_API_GW === '1',
      localizationTest: process.env.NUXT_PUBLIC_TEST_LOCALIZATION,
    },
  },

  alias: {
    '@atx-helper': resolve(__dirname, 'node_modules/@atx-eshop/eshop-components/dist/runtime/helpers'),
    '@atx-model': resolve(__dirname, 'node_modules/@atx-eshop/eshop-components/dist/runtime/core/model'),
  },

  sourcemap: true,
  compatibilityDate: '2024-11-01',

  vite: {
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern',
          additionalData:
            '@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_element" as element;',
        },

        sass: {
          api: 'modern',
          additionalData:
            '@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins"\n@use "@/assets/sass/_variables"\n',
        },
      },
    },
    server: {
      watch: {
        usePolling: true,
        interval: 100,
      },
    },
  },

  elementPlus: {
    importStyle: false,
    icon: false,
  },

  i18n: {
    lazy: true,
    strategy: 'no_prefix',
    langDir: 'lang',
    locales: getDynamicLocales(),
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n',
    },
    compilation: {
      strictMessage: false,
    },
  },

  pinia: {
    storesDirs: [
      './stores/**',
      './node_modules/@atx-eshop/eshop-components/dist/runtime/stores/**',
    ],
  },
})
