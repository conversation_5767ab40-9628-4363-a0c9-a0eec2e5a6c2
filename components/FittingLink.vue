<template>
  <a
    :href="`${externalUrl.eshop}${$t('fitting.cabin.url')}`" class="fitting-link" target="_blank"
    @click="trackPromoClicks([promo])"
  >
    <div class="fitting-icon">
      <svg width="24" height="20" viewBox="0 0 24 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M17.2727 19.9708L17.2619 19.9673L17.2518 19.9625C17.202 19.9376 17.1566 19.9051 17.1175 19.8662C17.0863 19.8362 16.9239 19.6915 16.3226 19.168C15.7233 18.6335 15.1534 18.069 14.6154 17.477L14.614 17.4755C13.9173 16.6944 13.2619 15.8801 12.6501 15.0355C11.9932 14.154 11.4449 13.2018 11.0163 12.1981C10.1479 12.0345 9.25326 11.833 8.35804 11.5988C7.43223 11.3719 6.49316 11.0957 5.56673 10.7779H5.56595C4.65544 10.4603 3.74835 10.0997 2.86951 9.70644C1.9808 9.31597 1.11308 8.88257 0.269638 8.4079C0.18019 8.36424 0.107905 8.29373 0.0636952 8.20699C0.0225045 8.12994 0.000698643 8.04463 4.0159e-05 7.95796V4.21345C-0.000853786 4.14293 0.0131853 4.07298 0.0412989 4.00787C0.0694125 3.94276 0.11101 3.88387 0.163546 3.83479C0.21443 3.78408 0.275518 3.74392 0.34307 3.71677C0.410622 3.68962 0.483213 3.67605 0.556398 3.67689C0.601732 3.677 0.646631 3.68542 0.6887 3.7017C0.732247 3.70615 0.773773 3.72173 0.808989 3.74681C1.27376 3.98562 1.86133 4.2724 2.55638 4.59948H2.55763C3.24161 4.92912 4.03028 5.27123 4.90194 5.61621C5.77361 5.96119 6.72735 6.29564 7.73881 6.61054C8.72534 6.90075 9.72803 7.13723 10.7421 7.31883C10.5158 6.50756 10.451 5.66229 10.5512 4.82776C10.6576 4.0044 10.984 3.22179 11.4985 2.55654C11.8131 2.15248 12.1867 1.79437 12.6076 1.49334C13.0635 1.16964 13.5585 0.900677 14.0818 0.692409C14.6421 0.459313 15.2269 0.285165 15.826 0.17299C16.4586 0.0559465 17.1015 -0.00196834 17.7457 5.10334e-05C18.4775 0.00193104 19.2069 0.0791644 19.9216 0.230436C20.6062 0.371866 21.2641 0.614545 21.8718 0.949863C22.4524 1.24792 22.9588 1.66424 23.3557 2.16991C23.7565 2.67849 23.9817 3.29543 23.9997 3.93389V4.30398C23.9996 4.41233 23.992 4.52055 23.9768 4.6279C23.9924 4.88581 23.9997 5.25785 23.9997 5.73396V7.68591C24.0091 8.44879 23.7974 9.19905 23.3883 9.85216C22.9494 10.523 22.3378 11.0729 21.6129 11.4482C20.8695 11.8438 20.0699 12.132 19.2396 12.3037H19.2371C18.2664 12.4941 17.2801 12.6015 16.2898 12.6246H15.2732C15.2136 12.6246 15.1537 12.6237 15.0939 12.6219C15.6174 13.2591 16.1924 13.8554 16.8135 14.4051C17.6234 15.093 17.7833 15.23 17.8205 15.2623C17.8879 15.3098 17.9372 15.3773 17.9609 15.4545C18.0006 15.5214 18.0209 15.5974 18.0199 15.6745V19.4641C18.0199 19.5266 18.0093 19.5888 17.9887 19.648C17.9686 19.708 17.9354 19.763 17.8912 19.8094C17.8408 19.8745 17.7746 19.9268 17.6985 19.9617L17.6885 19.9665L17.6779 19.9699C17.6086 19.9908 17.5364 20.001 17.4638 19.9999C17.3989 20.0002 17.3344 19.9903 17.2727 19.9708ZM15.0304 14.6606C15.083 14.7097 15.1246 14.7686 15.1527 14.8337C15.1808 14.8988 15.1948 14.9688 15.1939 15.0393V16.483C15.5684 16.915 15.9339 17.3021 16.2786 17.6341C16.5191 17.866 16.7373 18.0728 16.9306 18.2522V15.9047C16.6653 15.6909 16.2073 15.2843 15.566 14.6943C14.8541 14.0323 14.2079 13.3075 13.6359 12.5296C13.391 12.5145 13.1367 12.4929 12.8785 12.4652C12.6913 12.445 12.5015 12.4213 12.3118 12.3944C12.5682 12.9158 12.854 13.4233 13.1679 13.9146C13.4689 14.3556 13.7829 14.7833 14.104 15.1909V15.0393C14.1014 14.9065 14.1469 14.7771 14.2326 14.6731L14.2379 14.6666L14.2441 14.6608C14.2956 14.6109 14.3568 14.5713 14.4242 14.5442C14.4916 14.5172 14.5639 14.5033 14.637 14.5033C14.71 14.5033 14.7823 14.5172 14.8497 14.5442C14.9171 14.5713 14.9783 14.6109 15.0298 14.6608L15.0304 14.6606ZM11.8985 9.96239C11.9511 10.0115 11.9926 10.0704 12.0208 10.1355C12.0489 10.2006 12.0629 10.2705 12.062 10.3411V11.257C12.6875 11.3524 13.3125 11.4291 13.9222 11.485C14.5176 11.5398 15.1009 11.5697 15.6589 11.5745V10.659C15.6569 10.5904 15.6711 10.5223 15.7005 10.4597C15.7298 10.3972 15.7734 10.3419 15.8281 10.298C15.8788 10.2491 15.9391 10.2104 16.0056 10.1843C16.072 10.1583 16.1432 10.1453 16.2149 10.1462C16.3542 10.1521 16.4862 10.2081 16.5848 10.3031C16.6835 10.3982 16.7416 10.5254 16.7477 10.6597V11.547C17.4047 11.5178 18.0588 11.4452 18.7054 11.3297C19.2834 11.2299 19.8487 11.0708 20.3915 10.8551V9.7284C20.3887 9.66297 20.3996 9.59767 20.4238 9.53646C20.4479 9.47524 20.4847 9.41939 20.5319 9.3723C20.6349 9.27239 20.7746 9.21573 20.9207 9.21466C21.0668 9.21359 21.2075 9.2682 21.312 9.36658C21.3668 9.41056 21.4106 9.46599 21.44 9.52868C21.4693 9.59136 21.4835 9.65966 21.4814 9.7284V10.2829C21.8811 10.0088 22.2151 9.65518 22.4607 9.24598L22.4626 9.24297C22.7517 8.76198 22.9064 8.21695 22.9116 7.6611V6.85957C22.6432 7.12478 22.345 7.36029 22.0223 7.5617C21.512 7.87581 20.962 8.12548 20.3857 8.30459C19.7254 8.51646 19.0465 8.67052 18.3575 8.7649H18.3542C17.5587 8.85923 16.7578 8.90514 15.9562 8.90235C15.2488 8.90235 14.5078 8.87228 13.7534 8.81092C13.0028 8.75077 12.2263 8.65167 11.4444 8.51647H11.3991L11.3873 8.51467C10.2917 8.35232 9.20781 8.12354 8.14211 7.82968C7.1216 7.54215 6.15086 7.22815 5.25579 6.89491C4.36072 6.56166 3.53539 6.22586 2.7979 5.89261L2.79556 5.89156C2.15947 5.5908 1.58674 5.31635 1.0892 5.07198V7.64185L2.0409 8.14247C2.31159 8.27301 2.59039 8.40489 2.87216 8.53602V7.77644C2.86934 7.71104 2.88033 7.64576 2.90446 7.58458C2.9286 7.52339 2.96538 7.46757 3.01258 7.42049C3.11553 7.32056 3.25526 7.26388 3.40137 7.26278C3.54747 7.26168 3.68811 7.31626 3.79266 7.41463C3.84752 7.4586 3.89129 7.51403 3.92064 7.57671C3.95 7.6394 3.96417 7.7077 3.9621 7.77644V9.01424L5.36922 9.56524C5.81418 9.72118 6.2646 9.87337 6.71096 10.0185V9.22898C6.71125 9.08679 6.76994 8.95049 6.8742 8.84988C6.97845 8.74928 7.1198 8.69256 7.26732 8.69212C7.33864 8.69012 7.4095 8.70378 7.47454 8.73207C7.53957 8.76037 7.59708 8.80255 7.6427 8.85543C7.69342 8.9043 7.73348 8.96246 7.76052 9.02649C7.78755 9.09052 7.80102 9.15911 7.80012 9.22823V10.3411C8.3401 10.494 8.88569 10.6354 9.4227 10.7614C9.93615 10.8817 10.4487 10.9913 10.9487 11.0879V10.3411C10.9485 10.235 10.9811 10.1313 11.0422 10.043C11.1032 9.95481 11.1901 9.88606 11.2918 9.8455C11.3935 9.80494 11.5054 9.79439 11.6133 9.8152C11.7212 9.83601 11.8203 9.88724 11.8979 9.96239H11.8985ZM13.9293 5.64178H13.9283C13.5043 5.83639 13.1166 6.09726 12.781 6.41384C12.4569 6.72513 12.1884 7.08589 11.9864 7.48155C11.9823 7.49937 11.9766 7.51683 11.9694 7.53373C12.6544 7.62982 13.3267 7.70712 13.9687 7.76321C14.6516 7.82336 15.3202 7.85344 15.9562 7.85344C17.4718 7.85344 18.7792 7.69539 19.8423 7.3835C20.3317 7.24077 20.8024 7.04396 21.2449 6.79701C21.5646 6.61703 21.8567 6.39493 22.1127 6.13713L22.1035 6.11939L22.0833 6.10976L22.067 6.09472C21.5395 5.64757 20.9162 5.31794 20.2416 5.12927H20.2408C19.4338 4.88504 18.592 4.76502 17.7462 4.77362C17.5787 4.77362 17.4008 4.78099 17.2175 4.79588H17.2045C17.0402 4.79606 16.8762 4.8104 16.7145 4.83874L16.7007 4.84114H16.6722C15.7174 4.956 14.7898 5.22675 13.9293 5.64178ZM14.5564 1.62839C14.1091 1.80577 13.6854 2.03406 13.2943 2.30841C12.9414 2.55774 12.6274 2.85462 12.3621 3.18995C12.0328 3.6359 11.8014 4.14186 11.6818 4.67693C11.5824 5.14409 11.5587 5.62328 11.6116 6.09743C11.8516 5.81012 12.1227 5.54838 12.4204 5.31665C12.8255 5.00429 13.2732 4.74717 13.7511 4.55256C14.677 4.15106 15.6607 3.88705 16.6686 3.76952H16.6734C16.8625 3.75448 17.0466 3.74651 17.2209 3.74636C17.395 3.73132 17.5716 3.72365 17.7461 3.72365C18.7381 3.7152 19.725 3.86343 20.6673 4.16247C21.4173 4.37418 22.1121 4.73721 22.7054 5.22747C22.7657 5.07418 22.812 4.91608 22.8436 4.75497C22.8891 4.50305 22.9119 4.2478 22.9116 3.99209C22.8901 3.54217 22.7329 3.10807 22.4592 2.74302C22.1418 2.34912 21.7294 2.03588 21.2579 1.8305L21.2563 1.82975C20.7206 1.57157 20.155 1.37561 19.5713 1.24596C18.9724 1.11468 18.3601 1.0491 17.7459 1.05047C16.6537 1.0395 15.57 1.23587 14.5564 1.62839Z"
          fill="#2A2A2A"
        />
      </svg>
    </div>
    <div class="fitting-content">
      <p class="fitting-title">{{ $t('fitting.cabin.title') }}</p>
      <p class="fitting-caption">{{ $t('fitting.cabin.caption') }}</p>
    </div>
  </a>
</template>

<script lang="ts" setup>
import { useMagazineGtm } from '~/composables/useMagazineGtm'

const { t } = useI18n()
const { currentPageType } = usePageStore()
const { externalUrl } = useStoreAuth()

const promo = {
  name: `${t('fitting.cabin.title')}`,
  id: 1,
  creative: `${t('fitting.cabin.caption')}`,
  position: `mg-${currentPageType}`,
}

// Komponenta pro zobrazení odkazu na zkušební kabinku
const { trackPromoImpressions, trackPromoClicks } = useMagazineGtm()
trackPromoImpressions([promo])
</script>

<style lang="sass" scoped>
.fitting-link
  width: fit-content
  margin: 0 auto
  background: #ffeded
  border-radius: 12px
  text-decoration: none
  display: flex
  align-items: center
  padding: 14px 16px
  gap: 12px
  transition: all 0.2s ease-in-out
  color: #2A2A2A
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05)

  &:hover
    background: #FFDADF
    transform: translateY(-2px)
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1)

  .fitting-icon
    display: flex
    align-items: center
    justify-content: center
    background: white
    border-radius: 50%
    width: 40px
    height: 40px
    flex-shrink: 0
    color: #FF6B81

  .fitting-content
    display: flex
    flex-direction: column
    gap: 2px

  .fitting-title
    margin: 0
    font-weight: 600
    font-size: 16px
    line-height: 1.2

  .fitting-caption
    margin: 0
    font-size: 13px
    color: #666
    line-height: 1.2
</style>
