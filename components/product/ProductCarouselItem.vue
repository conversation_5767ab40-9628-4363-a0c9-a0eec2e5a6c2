<template>
  <el-row
    tag="a"
    :href="item.url"
    type="flex"
    align="top"
    class="widget-item"
    draggable="false"
    target="_blank"
    @click="trackProductClickGtm"
  >
    <PictureTag :breakpoints="['(min-width:480px)', '(max-width:479px)']" :rulesets="['prg11b', 'prg11s']" :src="item.imageInfo" :alt="item.name" draggable="false" />
    <TextParagraph ref="text" :text="item.name" :body-semi="1" draggable="false" class="widget-item__name" />
    <TagsPrice :price="Number(item.priceVat)" />
  </el-row>
</template>

<script setup lang="ts">
import type { IProductPromo } from '~/core/model/ecommerce.model'
import type { MagazineCommodity } from '~/core/model'

const props = defineProps({
  item: {
    type: Object as () => MagazineCommodity,
    default: () => ({} as MagazineCommodity),
  },
  promo: {
    type: Array as () => IProductPromo[],
    default: () => [] as IProductPromo[],
  },
})

const { trackProductClick } = useMagazineGtm()

const trackProductClickGtm = () => {
  trackProductClick(props.promo.filter(p => p.id === props.item.id))
}
</script>

<style lang="sass">
.widget-item
  flex-direction: column
  text-decoration: none
  height: 100%

  .el-image
    border-radius: 8px
    margin-bottom: ($grid-gutter/2)

  &__name
    align-self: stretch
    max-height: 48px
    overflow: hidden
</style>
