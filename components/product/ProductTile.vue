<template>
  <a
    :href="data.url"
    class="product-tile"
    target="_blank"
    @click="trackProductClickGtm"
  >
    <el-row type="flex" align="middle">
      <picture>
        <source media="(min-width:480px)" :srcset="`${runtimeConfig.public.imageUrl}/prg11b/${data.image}`" />
        <source media="(max-width:479px)" :srcset="`${runtimeConfig.public.imageUrl}/prg17/${data.image}`" />
        <img :src="`${runtimeConfig.public.imageUrl}/prg11b/${data.image}`" :alt="data.name" :title="data.name" />
      </picture>
      <el-row class="product-tile__description">
        <TextParagraph :text="data.name" :body-semi="1" />
        <TagsPrice :price="Number(data.priceVat)" />
        <TextParagraph :text="data.annotation" />
      </el-row>
    </el-row>
  </a>
</template>

<script setup lang="ts">
import type { IProductPromo } from '~/core/model/ecommerce.model'
import type { MagazineCommodity } from '~/core/model'

const runtimeConfig = useRuntimeConfig()

const props = defineProps({
  data: {
    type: Object as () => MagazineCommodity,
    default: () => ({} as MagazineCommodity),
  },
  promo: {
    type: Array as () => IProductPromo[],
    default: () => [] as IProductPromo[],
  },
})

const { trackProductClick } = useMagazineGtm()

const trackProductClickGtm = () => {
  trackProductClick(props.promo.filter(p => p.id === props.data.id))
}
</script>

<style lang="sass">
@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins" as *

.product-tile
  display: block
  text-decoration: none
  outline: none
  margin-bottom: 56px
  color: $paragraph-color

  @include media-breakpoint-up(md)
    margin-bottom: $grid-gutter *3

  &:focus
    outline-color: red

  &:hover
    h3
      text-decoration: underline

  &__description
    padding-left: 24px

  picture
    max-width: 174px
    border-radius: 8px
</style>
