<template>
  <el-row :id="blockData.htmlId" type="flex" justify="center" class="landing-info-images__wrapper">
    <ImageWithInfo
      :url="blockData.image1Url"
      :link="blockData.image1LinkUrl"
      :alt="blockData.image1Alt"
      :title="blockData.image1Title"
      :text="blockData.image1Text"
    />
    <ImageWithInfo
      :url="blockData.image2Url"
      :link="blockData.image2LinkUrl"
      :alt="blockData.image2Alt"
      :title="blockData.image2Title"
      :text="blockData.image2Text"
    />
    <ImageWithInfo
      :url="blockData.image3Url"
      :link="blockData.image3LinkUrl"
      :alt="blockData.image3Alt"
      :title="blockData.image3Title"
      :text="blockData.image3Text"
    />
  </el-row>
</template>

<script setup lang="ts">
import ImageWithInfo from './ImageWithInfo.Component.vue'
import type { ImageWithInfoBlockModel } from '~/core/model'

defineOptions({
  name: 'ImageWithInfoWrapperComponent',
})

defineProps({
  blockData: {
    type: Object as () => ImageWithInfoBlockModel,
    required: true,
  },
})
</script>

<style lang="sass" scoped>
@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins" as *

.landing-info-images__wrapper
  flex-direction: column
  gap: 48px

  @include media-breakpoint-up(md)
    flex-direction: row
    align-items: stretch
    gap: 24px
</style>
