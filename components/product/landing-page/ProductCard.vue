<template>
  <el-row :id="blockData.htmlId" class="product-card" :class="`product-card--${cardType}`" :style="buttonColorStyle">
    <el-row type="flex" class="product-card--inner">
      <el-col :sm="12" class="text-center">
        <el-image
          v-if="cardType === 'small'"
          :src="`${runtimeConfig.public.imageServerUrl}/12/${blockData.imageUrl}`"
          :alt="blockData.altText"
          :title="blockData.altText"
        />
        <el-image
          v-if="cardType === 'tile' || cardType === 'large'"
          :src="`${runtimeConfig.public.imageServerUrl}/11/${blockData.imageUrl}`"
          :alt="blockData.altText"
          :title="blockData.altText"
        />
      </el-col>
      <el-col :sm="12">
        <el-row type="flex" justify="center" class="column-direction product-card__text">
          <TextHeadingComponent :level="cardType === 'large' ? 3 : 4" :alternative="false" :text="blockData.title" />
          <TextParagraph :text="blockData.text" tag="p" class="mb-24" />
          <el-row>
            <a
              v-if="blockData.link.hasLink"
              :href="blockData.link.url"
              :title="blockData.link.title"
              target="_blank"
              class="el-button el-button--primary is-round product-card__button"
            >
              <template v-if="cardType !== 'tile'">
                <i class="atx-icon-shopping-bag" />
                <span>
                  {{ blockData.link.text.length > 0 ? blockData.link.text : t('product_card.buy') }}
                </span>
              </template>
              <template v-else>
                {{ blockData.link.text.length > 0 ? blockData.link.text : t('product_card.view') }}
              </template>
            </a>
          </el-row>
        </el-row>
      </el-col>
    </el-row>
  </el-row>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import type { ProductCardBlockModel } from '~/core/model'

defineOptions({
  name: 'ProductCard',
})

const runtimeConfig = useRuntimeConfig()
const { t } = useI18n()

const props = defineProps({
  blockData: {
    type: Object as () => ProductCardBlockModel,
    required: true,
  },
  cardType: {
    type: String,
    default: 'small',
  },
})

const buttonColorStyle = computed(() => {
  return {
    '--product-card-bg': `#${props.blockData.color}`,
    '--product-card-color': `#${props.blockData.textColor}`,
    '--product-card-btn-bg': `#${props.blockData.link.linkColor}`,
    '--product-card-btn-color': `#${props.blockData.link.linkTextColor}`,
    '--product-card-btn-bg-hover': `#${props.blockData.link.hoverLinkColor}`,
    '--product-card-btn-bg-color': `#${props.blockData.link.hoverLinkTextColor}`,
  }
})
</script>

<style lang="sass" scoped>
@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins" as *

.product-card
  &--inner
    border-radius: 16px
    background: var(--product-card-bg)
    color: var(--product-card-color)
    overflow: hidden

    @include media-breakpoint-down(md)
      flex-direction: column

  &--small
    overflow: hidden
    padding: 12px
    width: 100%

    @include media-breakpoint-up(sm)
      max-width: 50%

  &--tile
    .el-image
      border-radius: 16px

    .product-card__info
      button
        text-transform: uppercase

  &--tile &
    &--inner
      background-color: none

    &__text
      @include media-breakpoint-down(md)
        padding: 24px 0 0

  &__image
    min-width: 50%
    display: flex
    flex-direction: column
    @include media-breakpoint-up(lg)
      max-width: 50%

  &__text
    padding: ($grid-gutter * 2) $grid-gutter
    height: 100%

    @include media-breakpoint-up(md)
      padding: ($grid-gutter * 2)

  &__button
    background: var(--product-card-btn-bg)
    border-color: var(--product-card-btn-bg)
    color: var(--product-card-btn-color)

    &:hover
      background: var(--product-card-btn-bg-hover)
      border-color: var(--product-card-btn-bg-hover)
      color: var(--product-card-btn-bg-color)

@include media-breakpoint-up(md)
  .block__productcardtile:nth-of-type(even) .product-card .product-card--inner
    flex-direction: row-reverse
</style>
