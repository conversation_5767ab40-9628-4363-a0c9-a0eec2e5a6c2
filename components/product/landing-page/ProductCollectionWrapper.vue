<template>
  <el-row :id="blockData.htmlId" class="product-collection">
    <div class="product-collection__header">
      <picture class="product-collection__header__banner">
        <source media="(min-width:480px)" :srcset="`${runtimeConfig.public.imageUrl}/magazin/11/${blockData.imageUrl}`">
        <source media="(max-width:479px)" :srcset="`${runtimeConfig.public.imageUrl}/magazin/12/${blockData.imageUrl}`">
        <img
          :src="`${runtimeConfig.public.imageUrl}/magazin/11/${blockData.imageUrl}`"
          :alt="blockData.title"
          :title="blockData.title"
        >
      </picture>
      <div class="product-collection__header__info flex-column-center" :style="{ backgroundColor: bgColor }">
        <img
          v-if="blockData.icon !== null && blockData.icon !== ''"
          class="icon"
          :src="`${runtimeConfig.public.imageUrl}/magazin/${blockData.icon}`"
          alt="icon"
        >
        <TextHeading :text="blockData.title" :style="{ color: textColor }" />
        <TextParagraph :text="blockData.text" :style="{ color: textColor }" />
      </div>
    </div>
    <section class="product-collection__content" :class="{ 'product-collection__content--xxl': enlargeGridColCount }">
      <ProductLandingPageProductCollectionItem
        v-for="(product, index) in blockData.data"
        :key="index"
        :data="product"
        :bg-color="bgColor"
        :text-color="textColor"
      />
    </section>

    <div class="product-collection__footer text-center" :class="`links_${blockData.links.length}`">
      <el-row v-for="(link, i) in blockData.links" :key="i" class="product-collection__footer--column">
        <el-row
          v-if="link.hasLink"
          tag="a"
          :href="link.url"
          :title="link.title"
          class="product-collection__footer--link"
          :style="{ backgroundColor: bgColor, color: textColor }"
          target="_blank"
        >
          <span>{{ link.text }}</span>
          <i class="atx-icon-arrow-right" />
        </el-row>
      </el-row>
    </div>
  </el-row>
</template>

<script setup lang="ts">
import { useRuntimeConfig } from 'nuxt/app'
import { computed } from 'vue'
import type { ProductCollectionBlockModel } from '~/core/model/content.model'

defineOptions({
  name: 'ProductCollectionComponent',
})

const runtimeConfig = useRuntimeConfig()

const props = defineProps({
  blockData: {
    type: Object as () => ProductCollectionBlockModel,
    required: true,
  },
})

const bgColor = computed((): string => {
  return `#${props.blockData.color}`
})

const textColor = computed((): string => {
  return `#${props.blockData.textColor}`
})

const enlargeGridColCount = computed((): boolean => {
  return props.blockData.data.length > 12
})
</script>

<style lang="sass" scoped>
@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins" as *

.product-collection
  &__header
    text-align: center
    margin-bottom: 24px
    text-decoration: none
    display: block

    @include media-breakpoint-up(lg)
      display: flex
      flex-direction: row-reverse
      gap: 24px
      align-items: stretch

    &__banner
      @include media-breakpoint-down(lg)
        margin-bottom: 24px

      img
        border-radius: 16px
        aspect-ratio: 16 / 9

    &__info
      border-radius: 16px
      padding: 48px 32px
      @include media-breakpoint-up(lg)
        max-width: 33%

      img.icon
        padding: 20px

  &__content
    display: grid
    gap: 24px
    grid-template-columns: repeat(2, 1fr)
    grid-auto-rows: 1fr
    margin-bottom: $grid-gutter

    @include media-breakpoint-up(md)
      grid-template-columns: repeat(4, 1fr)
    &--xxl
      @include media-breakpoint-up(xl)
        grid-template-columns: repeat(5, 1fr)

  &__footer
    display: flex
    flex-wrap: wrap
    margin: 0 -12px
    width: 100%

    &--column
      flex-grow: 1
      min-width: 33.3334%
      padding: 12px

    &.links_4 &--column
      min-width: 50%

    &.links_5 &--column
      &:nth-child(1), &:nth-child(2)
        min-width: 50%

    &--link
      border-radius: 16px
      font-size: 18px
      font-weight: 700
      padding: 23px 58px
      color: $paragraph-color
      text-decoration: none
      display: block
      text-decoration: none
      width: 100%
      span
        display: inline-block

        & + i
          margin-left: 10px
          font-size: 24px
          vertical-align: middle
        &:hover
          text-decoration: underline
</style>
