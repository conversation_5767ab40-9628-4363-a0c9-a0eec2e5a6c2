<template>
  <div class="landing-info__image">
    <template v-if="link">
      <a :href="link" target="_blank">
        <picture>
          <source media="(min-width:480px)" :srcset="`${runtimeConfig.public.imageUrl}/magazin/13/${url}`" />
          <source media="(max-width:479px)" :srcset="`${runtimeConfig.public.imageUrl}/magazin/12/${url}`" />
          <img :src="`${runtimeConfig.public.imageUrl}/magazin/13/${url}`" :alt="alt" :title="alt" />
        </picture>
      </a>
    </template>
    <template v-else>
      <picture>
        <source media="(min-width:480px)" :srcset="`${runtimeConfig.public.imageUrl}/magazin/13/${url}`" />
        <source media="(max-width:479px)" :srcset="`${runtimeConfig.public.imageUrl}/magazin/12/${url}`" />
        <img :src="`${runtimeConfig.public.imageUrl}/magazin/13/${url}`" :alt="alt" :title="alt" />
      </picture>
    </template>

    <div class="image-info text-center">
      <TextHeadingComponent :level="4" :text="title" :alternative="false" :align="HeadlineAlignType.Center" />
      <TextParagraph :text="text" tag="p" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { HeadlineAlignType } from '~/core/constant'

defineOptions({
  name: 'ImageWithInfoComponent',
})

const runtimeConfig = useRuntimeConfig()

defineProps({
  url: {
    type: String,
    required: true,
  },
  link: {
    type: String,
    default: '',
  },
  alt: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  text: {
    type: String,
    required: true,
  },
})
</script>

<style lang="sass" scoped>
.landing-info__image
  flex: 1 1 0
  .image-info
    margin-top: 24px
    h4
      margin-bottom: 4px

  img
    width: 100%
    aspect-ratio: 1 / 1
    object-fit: cover
    border-radius: 8px
</style>
