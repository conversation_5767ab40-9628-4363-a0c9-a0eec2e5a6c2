<template>
  <el-row tag="a" :href="data.url" class="product-grid__item" target="_blank">
    <PictureTag :breakpoints="['(min-width:480px)', '(max-width:479px)']" :rulesets="['prg12', 'prg16']" :src="data.imageInfo" :alt="data.name" class="mb-8" />
    <el-row class="product-grid__item__info">
      <TextParagraph class="product-grid__item__name" :body-semi="1" :text="data.name" />
      <TagsPrice :price="Number(data.priceVat)" :bg-color="bgColor" :text-color="textColor" />
      <TextParagraph :body="3" :text="data.annotation" />
    </el-row>
  </el-row>
</template>

<script setup lang="ts">
import type { MagazineCommodity } from '~/core/model/base.model'

defineOptions({
  name: 'ProductCollectionItem',
})

defineProps({
  data: {
    type: Object as () => MagazineCommodity,
    required: true,
  },
  bgColor: {
    type: String,
    default: '',
  },
  textColor: {
    type: String,
    default: '',
  },
})
</script>

<style lang="sass" scoped>
.product-grid__item
  display: flex
  flex-direction: column
  margin-bottom: 24px
  text-decoration: none

  img
    aspect-ratio: 2 / 3
    border-radius: 16px

  &__name
    align-self: stretch
    max-height: 48px
    overflow: hidden

  &__info
    display: block
</style>
