<template>
  <el-row :id="blockData.htmlId" type="flex" justify="center" class="flex-wrap" style="margin: -12px" :gutter="24">
    <ProductLandingPageProductCardComponent v-for="block in blockData.blocks" :key="block.id" :block-data="block" card-type="small" />
  </el-row>
</template>

<script setup lang="ts">
import type { ProductCardWrapperBlockModel } from '~/core/model'

defineOptions({
  name: 'ProductCardWrapperComponent',
})

defineProps({
  blockData: {
    type: Object as () => ProductCardWrapperBlockModel,
    required: true,
  },
})
</script>
