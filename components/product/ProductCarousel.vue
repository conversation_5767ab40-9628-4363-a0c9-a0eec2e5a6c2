<template>
  <ClientOnly>
    <el-row
      v-if="data.data.length > 0"
      :id="data.htmlId"
      tag="section"
      class="widget-carousel"
      :data-gtm="`mg-${pageType}-carousel-${data.order}`"
    >
      <el-row v-if="data.title.length" class="widget-carousel__caption">
        <TextParagraph :caption="true" :caption-caps="true" :text="data.title" />
      </el-row>
      <el-col :md="20" style="">
        <carousel v-bind="carouselConfig" style="width: 100%; flex:1;">
          <slide v-for="(product, index) in data.data" :key="index">
            <ProductCarouselItem :item="product" :promo="promo" />
          </slide>
          <template #addons>
            <navigation v-if="data.data.length > 2" />
          </template>
        </carousel>
      </el-col>
      <el-row v-if="data.link.hasLink" class="widget-carousel__button">
        <a :href="data.link.url" :title="data.link.title" class="el-button el-button--secondary is-round" target="_blank">
          {{ data.link.text }}
        </a>
      </el-row>
    </el-row>
  </ClientOnly>
</template>

<script setup lang="ts">
import type { CarouselConfig } from 'vue3-carousel'
import type { ProductsByCodeContentBlockModel } from '~/core/model'
import type { IProductPromo } from '~/core/model/ecommerce.model'

const props = defineProps({
  data: {
    type: Object as () => ProductsByCodeContentBlockModel,
    default: () => ({} as ProductsByCodeContentBlockModel),
  },
})

const carouselConfig: Partial<CarouselConfig> = {
  mouseWheel: true,
  wrapAround: false,
  mouseDrag: true,
  itemsToScroll: 1,
  gap: 5,
  breakpoints: {
    992: { itemsToShow: props.data.data.length >= 3 ? 3 : 2 },
    768: { itemsToShow: 2, enabled: props.data.data.length > 2 },
  },
}

const pageStore = usePageStore()
// const rewriterStore = useRewriterStore()

const pageType = computed((): string => {
  return pageStore.currentPageType
})

// const isLandingPage = computed((): boolean => {
//   return rewriterStore.rewriterData.view.layoutType === 'landingpage'
// })

const promo = computed((): IProductPromo[] => {
  return props.data.data.map((i, index) => ({
    name: i.name,
    id: i.id,
    price: i.priceVat,
    position: index + 1,
    variant: i.color,
    list: `Carousel-${props.data.order}`,
  }))
})
const { trackProductImpressions } = useMagazineGtm()
trackProductImpressions(promo.value)
</script>

<style lang="sass">
.carousel
  --vc-pgn-background-color: rgba(0, 0, 0, 0.7)
  --vc-pgn-active-color: rgba(0, 0, 0, 1)
  --vc-nav-background: rgba(0, 0, 0, 0.7)
  --vc-nav-color: rgba(255, 255, 255, 1)
  --vc-nav-color-hover: rgba(255, 255, 255, 0.9)
  --vc-nav-border-radius: 50%
  --vc-png-bottom: 0

.widget-carousel
  width: 100%
  flex: 1
  align-items: center
  justify-content: center
  overflow: hidden

  &__caption
    text-align: center
    margin-bottom: $grid-gutter * 2

  &__button
    text-align: center
    margin-top: $grid-gutter * 2

  picture
    margin-bottom: 10px
</style>
