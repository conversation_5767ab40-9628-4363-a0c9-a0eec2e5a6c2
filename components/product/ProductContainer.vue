<template>
  <ul :id="data.htmlId" class="reset-ul" :data-gtm="`mg-${pageType}-tile-${data.order}`">
    <li v-for="(product, index) in data.data" :key="index">
      <ProductTileComponent :data="product" :promo="promo" />
    </li>
  </ul>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import ProductTileComponent from './ProductTile.vue'
import type { ProductsByCodeContentBlockModel } from '~/core/model'
import type { IProductPromo } from '~/core/model/ecommerce.model'
import { usePageStore } from '~/stores/page'

defineOptions({
  name: 'ProductTileContainerComponent',
})

const props = defineProps({
  data: {
    type: Object as () => ProductsByCodeContentBlockModel,
    default: () => ({} as ProductsByCodeContentBlockModel),
  },
})

const pageStore = usePageStore()

const pageType = computed((): string => {
  return pageStore.currentPageType
})

const promo = computed((): IProductPromo[] => {
  return props.data.data.map((i, index) => ({
    name: i.name,
    id: i.id,
    price: i.priceVat,
    position: index + 1,
    variant: i.color,
    list: `Tile-${props.data.order}`,
  }))
})

const { trackProductImpressions } = useMagazineGtm()
trackProductImpressions(promo.value)
</script>
