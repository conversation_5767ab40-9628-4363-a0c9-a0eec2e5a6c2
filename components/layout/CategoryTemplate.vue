<template>
  <el-row class="main__container">
    <el-row :gutter="32" class="category-main">
      <el-col :xs="24" :sm="8" :lg="6" class="category-main__menu">
        <slot name="menu" />
      </el-col>
      <el-col :xs="24" :sm="16" :lg="18" class="category-main__content">
        <slot name="content" />
      </el-col>
      <el-col :xs="24" :sm="8" :lg="6" class="category-main__aside">
        <slot name="aside" />
      </el-col>
    </el-row>
  </el-row>
</template>

<style lang="sass">
@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins" as *

.category-main
  display: block
  width: 100%
  &__menu
    float: left
  &__content
    float: right
    padding-top: ($grid-gutter * 2)
    @include media-breakpoint-up(lg)
      padding-top: ($grid-gutter * 3)
  &__aside
    clear: left
</style>
