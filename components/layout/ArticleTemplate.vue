<template>
  <div class="main__container">
    <slot name="headline" />
    <div class="article-container">
      <article>
        <slot name="content" />
      </article>
      <aside>
        <slot name="aside" />
      </aside>
      <section>
        <slot name="nav" />
      </section>
    </div>
  </div>
</template>

<style lang="sass" scoped>
.main__container
  display: flex
  flex-direction: column
  align-items: center
  justify-content: center
  padding-right: 40px
  padding-left: 40px
  max-width: 1440px
  margin: 0 auto
</style>
