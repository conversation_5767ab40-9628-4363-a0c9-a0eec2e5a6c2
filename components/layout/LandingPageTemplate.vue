<template>
  <el-row class="main__container">
    <slot name="headline" />

    <article class="landingpage-container">
      <slot name="content" />
      <el-row tag="section">
        <slot name="nav" />
      </el-row>
    </article>
  </el-row>
</template>

<style lang="sass">
@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins" as *

.main__container
  align-items: center
  justify-content: center
  padding-right: 40px
  padding-left: 40px

.landingpage-container
  position: relative
  display: flex
  flex-direction: column
  align-items: center
  justify-content: center
  padding-right: 40px
  padding-left: 40px
  max-width: 1440px

  > [class^='block__']
    margin-bottom: 40px
    @include media-breakpoint-up(md)
      margin-bottom: 72px

  > .block
    &__headline
      margin-bottom: ($grid-gutter)
    @include media-breakpoint-up(lg)
      margin-bottom: $grid-gutter + ($grid-gutter/2)
</style>
