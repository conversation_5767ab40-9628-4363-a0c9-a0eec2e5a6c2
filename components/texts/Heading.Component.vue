<template>
  <h1 v-if="level === 1" :class="[{ alt: alternative }, alignClass]" v-html="text" />
  <h2 v-else-if="level === 2" :id="id" :class="[{ alt: alternative }, alignClass]" v-html="text" />
  <h3 v-else-if="level === 3" :id="id" :class="[{ alt: alternative }, alignClass]" v-html="text" />
  <h4 v-else-if="level === 4" :id="id" :class="[{ alt: alternative }, alignClass]" v-html="text" />
  <h5 v-else-if="level === 5" :id="id" :class="[{ alt: alternative }, alignClass]" v-html="text" />
  <h6 v-else-if="level === 6" :id="id" :class="[{ alt: alternative }, alignClass]" v-html="text" />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { HeadlineAlignType } from '~/core/constant'

defineOptions({
  name: 'HeadingComponent',
})

interface Props {
  level?: number
  text?: string
  alternative?: boolean
  center?: boolean
  align?: number
  id?: string
}

const props = withDefaults(defineProps<Props>(), {
  level: 3,
  text: '',
  alternative: true,
  center: false,
  align: HeadlineAlignType.Left,
  id: '',
})

const alignClass = computed(() => {
  switch (props.align) {
    case HeadlineAlignType.Center:
      return 'text-center'
    case HeadlineAlignType.Right:
      return 'text-right'
    case HeadlineAlignType.Left:
      return 'text-left'
    default:
      return 'text-left'
  }
})
</script>
