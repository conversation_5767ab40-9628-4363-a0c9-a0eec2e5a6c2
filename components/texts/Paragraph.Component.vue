<template>
  <section
    :class="{
      'body-n': body === 1,
      'body-l': body === 2,
      'body-s': body === 3,
      'body-n-semi': bodySemi === 1,
      'body-l-semi': bodySemi === 2,
      'body-s-semi': bodySemi === 3,
      'description': description,
      'caption': caption,
      'caption-caps': captionCaps,
    }"
  >
    <div v-if="tag === ''" v-html="text" />
    <p v-if="tag === 'p'" v-html="text" />
  </section>
</template>

<script setup lang="ts">
defineProps({
  body: {
    type: Number,
    default: 0,
  },

  bodySemi: {
    type: Number,
    default: 0,
  },

  description: {
    type: Boolean,
    default: false,
  },

  caption: {
    type: Boolean,
    default: false,
  },

  captionCaps: {
    type: Boolean,
    default: false,
  },

  text: {
    type: String,
    default: '',
  },

  tag: {
    type: String,
    default: '',
  },
})
</script>
