<template>
  <div v-if="authors.length" class="avatar-group vertical">
    <TextParagraph
      :caption-caps="true" :text="$t('avatar.group.title')"
      class="avatar-group__title"
    />
    <ul class="reset-ul avatar-group__list">
      <li v-for="(author, index) in authors" :key="index" :data-gtm="`mg-${pageType}-author-link-${author.url}`">
        <Avatar :author="author" />
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
const pageStore = usePageStore()
const authorStore = useAuthorStore()

await authorStore.fetchAuthors()

const pageType = computed(() => pageStore.currentPageType)
const authors = computed(() => authorStore.authors)
</script>

<style lang="sass">
@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins" as *

.avatar-group
  text-align: center
  @include media-breakpoint-up(md)
    padding: 48px 0 0 0

  &__list
    display: flex
    flex-wrap: wrap

  li
    padding-top: 24px
    flex-basis: 50%
    @media (min-width: 768px)
      flex-basis: 100%
</style>
