<template>
  <NuxtLink :to="`/${author.url}`" class="avatar">
    <div class="avatar__image">
      <img :src="getImageUrl(author.imageUrl)" :alt="author.name">
    </div>
    <div class="avatar__content">
      <p class="avatar__title body-s-semi">
        {{ author.name }}
      </p>
      <p class="avatar__caption caption">
        {{ author.jobPosition }}
      </p>
    </div>
  </NuxtLink>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { AuthorBaseModel } from '~/core/model'

const runtimeConfig = useRuntimeConfig()

defineProps({
  author: {
    type: Object as PropType<AuthorBaseModel>,
    default: () => new AuthorBaseModel(),
  },
})

// Funkce pro získání URL obrázku
const getImageUrl = (imageUrl: string) => {
  // Pokud URL již obsahuje http/https, použijeme ji přímo
  if (imageUrl.startsWith('http')) {
    return imageUrl
  }

  // Pokud máme k dispozici imageUrl v konfiguraci, použijeme ji
  if (runtimeConfig.public.imageUrl) {
    return `${runtimeConfig.public.imageUrl}/magazin/17/${imageUrl}`
  }

  // Fallback - použijeme relativní cestu
  return `/images/authors/${imageUrl}`
}
</script>

<style lang="scss">
.avatar {
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  color: var(--paragraph-color);

  &.vertical, .vertical & {
    flex-direction: column;
    text-align: center;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
  }

  &__image {
    flex: 1;

    img {
      max-width: 48px;
      border-radius: 50px;

      @media (max-width: 768px) {
        max-width: 40px;
      }
    }
  }

  &__title {
    margin-bottom: 0;
  }

  &__content {
    padding: 0 var(--grid-gutter);
  }

  &__caption {
    margin-top: 0;
  }
}
</style>
