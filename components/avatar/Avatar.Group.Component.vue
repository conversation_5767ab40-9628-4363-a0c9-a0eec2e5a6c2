<template>
  <div v-if="authors.length" class="avatar-group vertical">
    <ParagraphComponent
      :caption-caps="true" :text="$t('avatar.group.title')"
      class="avatar-group__title"
    />
    <ul class="reset-ul avatar-group__list">
      <li v-for="(author, index) in authors" :key="index" :data-gtm="`mg-${pageType}-author-link-${author.url}`">
        <AvatarComponent :author="author" />
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { usePageStore } from '~/stores/page'
import { useAuthorStore } from '~/stores/author'
import ParagraphComponent from '~/components/texts/Paragraph.Component.vue'

const pageStore = usePageStore()
const authorStore = useAuthorStore()

// Načtení autorů při vytvoření komponenty
onMounted(() => {
  authorStore.fetchAuthors()
})

const pageType = computed(() => pageStore.currentPageType)
const authors = computed(() => authorStore.authors)
</script>

<style lang="sass">
@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins" as *

.avatar-group
  text-align: center
  @include media-breakpoint-up(md)
    padding: 48px 0 0 0

  &__list
    display: flex
    flex-wrap: wrap

  li
    padding-top: 24px
    flex-basis: 50%
    @media (min-width: 768px)
      flex-basis: 100%
</style>
