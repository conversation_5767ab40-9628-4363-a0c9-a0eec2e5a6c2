<template>
  <div :id="data.htmlId" class="interview-block">
    <div v-for="(item, index) in data.questions" :key="index" class="interview-block__item">
      <Paragraph :text="item.question" :body-semi="2" />
      <Paragraph :text="item.answer" :body="1" />
    </div>
  </div>
</template>

<script setup lang="ts">
import Paragraph from './Paragraph.Component.vue'

defineOptions({
  name: 'QuestionAnswerComponent',
})

defineProps({
  data: {
    type: Object,
    required: true,
  },
})
</script>

<style lang="scss" scoped>
.interview-block {
  &__item {
    & + & {
      margin-top: calc(var(--grid-gutter) * 2);
    }

    section + section {
      margin-top: 8px;
    }
  }
}
</style>
