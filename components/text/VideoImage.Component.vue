<template>
  <div class="c-video-image">
    <div v-if="blockData.layoutType === 1" class="c-video-image-video__left">
      <a
        :href="blockData.linkLeft"
        class="c-video-image-video__left-btn-video btn btn-primary btn-sm"
      >{{ blockData.linkText }}</a>
      <VideoComponent :data="blockData.video" />
    </div>
    <template v-else>
      <a
        class="c-video-image__left hidden-xs-only"
        :title="blockData.imageLeft.altText"
        :href="blockData.linkLeft"
        :style="`background-image: url('${runtimeConfig.public.imageServerUrl}/13/${blockData.imageLeft.imageUrl}')`"
      />
      <a
        class="c-video-image__left hidden-sm-and-up"
        :title="blockData.mobileImageLeft.altText"
        :href="blockData.mobileImageLeft.linkUrl"
        :style="`background-image: url('${runtimeConfig.public.imageServerUrl}/13/${blockData.mobileImageLeft.imageUrl}')`"
      />
    </template>
    <a
      :title="blockData.imageRight.altText"
      :href="blockData.linkRight"
      class="c-video-image__right"
      :style="`background-image: url('${runtimeConfig.public.imageServerUrl}/13/${blockData.imageRight.imageUrl}')`"
    />
  </div>
</template>

<script setup lang="ts">
import VideoComponent from './Video.Component.vue'
import { useRuntimeConfig } from '#imports'
import type { VideoImageBlockModel } from '~/core/model/content.model'

defineOptions({
  name: 'VideoImageComponent',
})

defineProps({
  blockData: {
    type: Object as () => VideoImageBlockModel,
    required: true,
  },
})

const runtimeConfig = useRuntimeConfig()
</script>

<style lang="scss" scoped>
.c-video-image {
  display: flex;
  gap: var(--grid-gutter);

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }

  &-video__left {
    flex: 1;
    position: relative;

    @media (max-width: 768px) {
      max-width: 343px;
    }

    .c-video, video {
      height: 100%;
    }

    &-btn-video {
      position: absolute;
      z-index: 1;
      right: 10px;
      top: 10px;
    }
  }

  &__left {
    flex: 1;
  }

  &__right {
    width: 34.4%;
    aspect-ratio: 1;

    .article-header & {
      width: 39.25%;
    }
  }

  &__left, &__right {
    display: block;
    background-position: 50%;
    background-size: cover;

    @media (max-width: 768px) {
      max-width: 343px;
      width: 100%;
      aspect-ratio: 1;
    }
  }
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  text-decoration: none;

  &.btn-primary {
    background-color: var(--primary-color);
    color: white;
  }

  &.btn-sm {
    padding: 8px 12px;
    font-size: 12px;
  }
}

.hidden-xs-only {
  @media (max-width: 575px) {
    display: none !important;
  }
}

.hidden-sm-and-up {
  @media (min-width: 576px) {
    display: none !important;
  }
}
</style>
