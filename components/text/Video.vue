<template>
  <div v-if="data" class="c-video">
    <video
      :width="data.width"
      :height="data.height"
      :autoplay="data.autoPlay"
      :controls="data.controls"
      :loop="data.loop"
      :muted="data.muted"
      :poster="data.imageUrl"
    >
      <source :src="data.videoUrl" type="video/mp4" />
      Your browser does not support the video tag.
    </video>
    <ParagraphComponent v-if="data.description" :description="true" :text="data.description" />
  </div>
</template>

<script setup lang="ts">
import ParagraphComponent from './Paragraph.vue'
import type { VideoContentBlockModel } from '~/core/model/content.model'

defineOptions({
  name: 'VideoComponent',
})

defineProps({
  data: {
    type: Object as () => VideoContentBlockModel,
    required: true,
  },
})
</script>

<style lang="scss" scoped>
.c-video {
  video {
    max-width: 100%;
    height: auto;
    vertical-align: middle;
    background: #fafafa;
  }
}
</style>
