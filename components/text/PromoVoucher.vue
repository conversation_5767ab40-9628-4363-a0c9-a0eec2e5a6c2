<template>
  <section class="voucher-component text-center" :style="{ 'background-color': bgColor }">
    <h2 class="mb-16 alt" :style="{ color: textColor }">
      {{ blockData.title }}
    </h2>
    <div class="mb-20" :style="{ color: textColor }" v-html="blockData.text" />
    <div class="voucher-boxes">
      <div
        v-for="(box, index) in blockData.promoVoucherBoxes"
        :key="index"
        class="voucher-component__column"
      >
        <div class="voucher-component__box">
          <img :src="box.icon" :alt="box.title" class="mb-20" style="height: 50px" />
          <h3 class="mb-20 alt" v-html="box.title" />
          <p class="mb-20" v-html="box.text" />
          <a v-if="box.link.hasLink" :href="box.link.url" class="btn btn-primary rounded">
            <i class="atx-icon-shopping-bag" /><span>{{ box.link.text }}</span>
          </a>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { PromoVoucherBlockModel } from '~/core/model/content.model'

defineOptions({
  name: 'PromoVoucher',
})

const props = defineProps({
  blockData: {
    type: Object as () => PromoVoucherBlockModel,
    required: true,
  },
})

const bgColor = computed(() => `#${props.blockData.color}`)
const textColor = computed(() => `#${props.blockData.textColor}`)
</script>

<style lang="scss" scoped>
.voucher-component {
  background: #ECECED;
  padding: 64px 16px;
  margin: 0 -16px;

  @media (min-width: 768px) {
    padding: 80px;
    margin: 0;
  }

  &__column {
    margin-top: 40px;
    width: 100%;

    @media (min-width: 768px) {
      width: calc(50% - 16px);
      margin-left: 8px;
      margin-right: 8px;
    }
  }

  &__box {
    padding: 80px 40px;
    background: #fff;
    border-radius: 8px;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    > a {
      margin-top: auto;
    }
  }
}

.voucher-boxes {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  font-weight: 500;
  text-decoration: none;

  &.btn-primary {
    background-color: var(--primary-color);
    color: white;
  }

  &.rounded {
    border-radius: 50px;
  }

  i {
    margin-right: 8px;
  }
}
</style>
