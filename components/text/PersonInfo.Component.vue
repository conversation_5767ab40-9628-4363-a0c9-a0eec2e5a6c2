<template>
  <div class="person-info">
    <div
      class="person-info__image"
      :style="`background-image: url('${runtimeConfig.public.imageServerUrl}/16/${data.imageUrl}')`"
      :title="data.name"
    />
    <div class="flex-1">
      <HeadingComponent :alt="true" :level="2" :text="data.name" />
      <p class="person-info__caption caption-caps" v-html="data.jobPosition" />
      <ParagraphComponent :text="data.description" />
    </div>
  </div>
</template>

<script setup lang="ts">
import HeadingComponent from './Heading.Component.vue'
import ParagraphComponent from './Paragraph.Component.vue'
import { useRuntimeConfig } from '#imports'

defineOptions({
  name: 'PersonInfoComponent',
})

defineProps({
  data: {
    type: Object,
    required: true,
  },
})

const runtimeConfig = useRuntimeConfig()
</script>

<style lang="scss" scoped>
.person-info {
  display: flex;
  border-top: 1px solid var(--divider-color);
  padding-top: calc(var(--grid-gutter) * 3);

  @media (max-width: 768px) {
    flex-direction: column;
  }

  &__image {
    margin: 0 calc(var(--grid-gutter) * 2) calc(var(--grid-gutter) * 2) 0;
    width: 156px;
    height: 156px;
    border-radius: 50%;
    background: center / cover;
  }

  &__title {
    margin-bottom: 0;
  }

  &__caption {
    margin: calc(var(--grid-gutter) / 2) 0;
  }
}
</style>
