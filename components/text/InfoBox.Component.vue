<template>
  <div class="infobox">
    <HeadingComponent class="infobox__title" :alt="true" :level="3" :text="data.title" />
    <ParagraphComponent :body="3" :text="data.text" />
    <VideoComponent v-if="data.hasVideo && data.videoData !== null" :data="data.videoData" />
    <PictureComponent v-if="data.hasImage && data.imageData !== null" :data="data.imageData" />
  </div>
</template>

<script setup lang="ts">
import HeadingComponent from './Heading.Component.vue'
import ParagraphComponent from './Paragraph.Component.vue'
import VideoComponent from './Video.Component.vue'
import PictureComponent from './Picture.Component.vue'
import type { TipContentBlockModel } from '~/core/model/content.model'

defineOptions({
  name: 'InfoboxComponent',
})

defineProps({
  data: {
    type: Object as () => TipContentBlockModel,
    required: true,
  },
})
</script>

<style lang="scss" scoped>
.infobox {
  border-left: 4px var(--infobox-border-color) solid;
  padding: 12px 24px;

  &__title {
    font-style: italic;
    font-size: 20px;
    line-height: 1.35;
  }
}
</style>
