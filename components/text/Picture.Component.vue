<template>
  <div v-if="data.imageUrl" class="c-picture">
    <template v-if="data.linkUrl">
      <a :href="data.linkUrl" target="_blank">
        <picture>
          <source media="(min-width:992px)" :srcset="`${runtimeConfig.public.imageServerUrl}/19/${data.imageUrl}`" />
          <source media="(min-width:576px)" :srcset="`${runtimeConfig.public.imageServerUrl}/18/${data.imageUrl}`" />
          <source media="(max-width:576px)" :srcset="`${runtimeConfig.public.imageServerUrl}/16/${data.imageUrl}`" />
          <img
            :src="`${runtimeConfig.public.imageServerUrl}/19/${data.imageUrl}`"
            :alt="data.altText"
            :title="data.altText"
            loading="lazy"
          />
        </picture>
      </a>
    </template>
    <template v-else>
      <picture>
        <source media="(min-width:992px)" :srcset="`${runtimeConfig.public.imageServerUrl}/19/${data.imageUrl}`" />
        <source media="(min-width:576px)" :srcset="`${runtimeConfig.public.imageServerUrl}/18/${data.imageUrl}`" />
        <source media="(max-width:576px)" :srcset="`${runtimeConfig.public.imageServerUrl}/16/${data.imageUrl}`" />
        <img
          :src="`${runtimeConfig.public.imageServerUrl}/19/${data.imageUrl}`"
          :alt="data.altText"
          :title="data.altText"
          loading="lazy"
        />
      </picture>
    </template>
    <ParagraphComponent v-if="data.title" :description="true" :text="data.title" />
  </div>
</template>

<script setup lang="ts">
import ParagraphComponent from './Paragraph.Component.vue'
import { useRuntimeConfig } from '#imports'
import type { ImageContentBlockModel } from '~/core/model/content.model'

defineOptions({
  name: 'PictureComponent',
})

defineProps({
  data: {
    type: Object as () => ImageContentBlockModel,
    required: true,
  },
})

const runtimeConfig = useRuntimeConfig()
</script>
