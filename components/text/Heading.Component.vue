<template>
  <h1 v-if="level === 1" :class="[{ alt: alternative }, alignClass]" v-html="text" />
  <h2 v-else-if="level === 2" :id="id" :class="[{ alt: alternative }, alignClass]" v-html="text" />
  <h3 v-else-if="level === 3" :id="id" :class="[{ alt: alternative }, alignClass]" v-html="text" />
  <h4 v-else-if="level === 4" :id="id" :class="[{ alt: alternative }, alignClass]" v-html="text" />
  <h5 v-else-if="level === 5" :id="id" :class="[{ alt: alternative }, alignClass]" v-html="text" />
  <h6 v-else-if="level === 6" :id="id" :class="[{ alt: alternative }, alignClass]" v-html="text" />
</template>

<script setup lang="ts">
import { computed, defineOptions, defineProps } from 'vue'
import { HeadlineAlignType } from '~/core/constant'

defineOptions({
  name: 'HeadingComponent',
})

const props = defineProps({
  level: {
    type: Number,
    default: 3,
  },
  text: {
    type: String,
    default: '',
  },
  alternative: {
    type: Boolean,
    default: true,
  },
  center: {
    type: Boolean,
    default: false,
  },
  align: {
    type: Number,
    default: HeadlineAlignType.Left,
  },
  id: {
    type: String,
    default: '',
  },
})

const alignClass = computed(() => {
  switch (props.align) {
    case HeadlineAlignType.Center:
      return 'text-center'
    case HeadlineAlignType.Right:
      return 'text-right'
    case HeadlineAlignType.Left:
      return 'text-left'
    default:
      return 'text-left'
  }
})
</script>
