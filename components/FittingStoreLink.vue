<template>
  <div class="fitting-store">
    <TextParagraph
      :caption-caps="true"
      :text="$t('fitting.store.title')"
      class="fitting-store__header"
    />
    <div class="fitting-store__image">
      <img src="~/assets/images/brafittingstore.png" alt="Astratex Poradna v Praze" />
    </div>
    <TextHeading
      :level="5"
      :text="$t('fitting.store.name')"
      class="fitting-store__title"
    />
    <TextParagraph
      :body="3"
      :text="$t('fitting.store.description')"
      class="fitting-store__description"
    />
    <p class="fitting-store__link">
      <a
        :href="$t('fitting.store.url', { url: eshopUrl })"
        target="_blank"
      >{{ $t('fitting.store.link') }}</a>
    </p>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useMagazineGtm } from '~/composables/useMagazineGtm'
import { usePageStore } from '~/stores/page'

const { trackPromoImpressions } = useMagazineGtm()

// Definice typu pro promo položku
interface PromoItem {
  name: string
  id: number
  creative: string
  position: string
}

// Deklarace globálních typů
declare global {
  interface Window {
    $ecommerce?: {
      trackPromoClicks: (promos: PromoItem[]) => void
      trackPromoImpressions: (promos: PromoItem[]) => void
    }
  }
}

const { t } = useI18n()
const config = useRuntimeConfig()
const pageStore = usePageStore()

const eshopUrl = computed(() => {
  return config.public.eshopUrl || ''
})

const promo = computed<PromoItem>(() => {
  return {
    name: t('fitting.store.title'),
    id: 1,
    creative: t('fitting.store.name'),
    position: `mg-${pageStore.currentPageType}`,
  }
})

trackPromoImpressions([promo.value])
</script>

<style lang="sass">
@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins" as *

.fitting-store
  display: flex
  flex-direction: column
  align-items: center
  text-align: center
  max-width: 400px
  margin: 0 auto
  padding: 20px 0
  margin-top: 32px
  border-top: 1px solid gray
  @include media-breakpoint-up(md)
    padding: 48px 0

  &__header
    text-transform: uppercase
    margin-bottom: 16px

  &__image
    width: 100%
    margin-bottom: 16px
    border-radius: 4px
    overflow: hidden

    img
      width: 100%
      height: auto
      display: block

  &__title
    margin-bottom: 8px

  &__description
    margin-bottom: 16px
    max-width: 320px

  &__link
    margin-top: 8px
    font-size: 14px

    a
      color: var(--color-primary)
      text-decoration: underline
      font-weight: 500
</style>
