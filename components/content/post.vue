<template>
  <Component :is="layoutComponent">
    <template #headline>
      <ArticleHeader v-if="!isVideo" :is-landing-page="isLandingPage" />
      <ArticleVideoHeaderComponent v-else />
    </template>
    <template #content>
      <TextParagraph v-if="!isLandingPage" :text="post.perex" :body-semi="2" class="block__perex" />
      <div v-for="(block, index) in post.contentBlocks" :key="block.order" :class="blockClass(block)">
        <!-- <pre>{{ JSON.stringify(block, null, 2) }}</pre> -->
        <TextHeading
          v-if="block.type === Block.headline"
          :id="block.htmlId"
          :text="(block as HeadlineContentBlockModel).text"
          :align="(block as HeadlineContentBlockModel).headlineAlignType"
          :level="(block as HeadlineContentBlockModel).headlineType"
        />
        <TextParagraph v-else-if="block.type === Block.text" :text="(block as TextContentBlockModel).text" />
        <TextPicture v-else-if="block.type === Block.image" :data="block as ImageContentBlockModel" />
        <ProductCarousel v-else-if="block.type === Block.productsbycode && (block as ProductsByCodeContentBlockModel).presentationType === 0" :data="block as ProductsByCodeContentBlockModel" />
        <ProductContainer v-else-if="block.type === Block.productsbycode && (block as ProductsByCodeContentBlockModel).presentationType === 1" :data="block as ProductsByCodeContentBlockModel" />
        <TextInfoBox v-else-if="block.type === Block.tip" :data="block as TipContentBlockModel" />
        <TextVideo v-else-if="block.type === Block.video && !isVideo && index !== 0" :data="block as VideoContentBlockModel" />
        <TextPersonInfo v-else-if="block.type === Block.personinfo" :data="block" />
        <TextSeparator v-else-if="block.type === Block.separator" />
        <TextQuestionAnswer v-else-if="block.type === Block.interview" :data="block" />
        <ProductLandingPageProductCardWrapper v-else-if="block.type === Block.ProductCardWrapper" :block-data="block as ProductCardWrapperBlockModel" />
        <ProductLandingPageProductCard v-else-if="block.type === Block.ProductCardLarge" :block-data="block as ProductCardBlockModel" card-type="large" />
        <ProductLandingPageProductCard v-else-if="block.type === Block.ProductCardTile" :block-data="block as ProductCardBlockModel" card-type="tile" />
        <ProductLandingPageImageWithInfoWrapper v-if="block.type === Block.ImageWithInfo" :block-data="block as ImageWithInfoBlockModel" />
        <ProductLandingPageProductCollectionWrapper v-if="block.type === Block.ProductCollection" :block-data="block as ProductCollectionBlockModel" />
        <ArticleImageCarousel v-if="block.type === Block.ImageCarousel" :block-data="block as ImageCarouselModel" />
        <TextVideoImage v-if="block.type === Block.VideoImage" :block-data="block as VideoImageBlockModel" />
        <TextPromoVoucher v-if="block.type === Block.PromoVoucher" :block-data="block as PromoVoucherBlockModel" />
      </div>

      <el-row v-if="onlyForCzSkHuPlRoBg" class="hidden-md-and-up">
        <ArticleSeparator />
        <TagsGroup />
      </el-row>
    </template>
    <template #nav>
      <template v-if="onlyForCzSkHuPlRoBg">
        <el-col class="hidden-sm-and-down">
          <ArticleSeparator />
          <el-col :lg="{ span: 20, offset: 3 }">
            <TagsGroup />
          </el-col>
        </el-col>
        <el-col align="center" type="flex" justify="center" :lg="{ span: 20, offset: 2 }">
          <AstratexLogo class="hidden-md-and-up article-logo" />
          <MenuComponent />
        </el-col>
      </template>
    </template>
    <template #aside>
      <ArticleCardSuggestionList />
    </template>
  </Component>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { SiteId } from '@atx-helper'
import type { WithContext, BreadcrumbList, NewsArticle } from 'schema-dts'
import { useHead } from '#imports'

import type { IRewriterView } from '~/core/model/rewrite.model'
import type { BaseContentBlockModel, HeadlineContentBlockModel, ImageCarouselModel, ImageContentBlockModel, ImageWithInfoBlockModel, ProductCardBlockModel, ProductCardWrapperBlockModel, ProductCollectionBlockModel, ProductsByCodeContentBlockModel, PromoVoucherBlockModel, TextContentBlockModel, TipContentBlockModel, VideoContentBlockModel, VideoImageBlockModel } from '~/core/model/content.model'
import { TagsGroup } from '#components'

enum Block {
  headline = 'HEADLINE',
  tip = 'TIP',
  text = 'TEXT',
  image = 'IMAGE',
  productsbycode = 'PRODUCTSBYCODE',
  video = 'VIDEO',
  personinfo = 'PERSONINFO',
  separator = 'SEPARATOR',
  interview = 'QUESTIONANSWER',
  ImageWithInfo = 'IMAGEWITHINFO',
  ProductCardWrapper = 'WRAPPER_PRODUCTCARD',
  ProductCardLarge = 'PRODUCTCARDLARGE',
  ProductCardTile = 'PRODUCTCARDTILE',
  ProductCollection = 'PRODUCTCOLLECTION',
  ImageCarousel = 'IMAGECAROUSEL',
  VideoImage = 'VIDEOIMAGE',
  PromoVoucher = 'PROMOVOUCHER',
}

const rewriterStore = useRewriterStore()
const appData = useStoreAuth()
const { t } = useI18n()

// Computed properties
const post = computed<IRewriterView>(() => rewriterStore.rewriterData.view)
const isVideo = computed<boolean>(() => post.value?.layoutType === 'video')
const isLandingPage = computed<boolean>(() => post.value?.layoutType === 'landingpage')
const layoutComponent = computed(() => {
  if (isLandingPage.value) {
    return resolveComponent('LayoutLandingPageTemplate')
  }
  return resolveComponent('LayoutArticleTemplate')
})

const onlyForCzSkHuPlRoBg = computed<boolean>(() => {
  switch (appData.info.siteId) {
    case SiteId.CZ:
    case SiteId.SK:
    case SiteId.HU:
    case SiteId.PL:
    case SiteId.RO:
    case SiteId.BG:
      return true
    default:
      return false
  }
})

// Methods
const blockClass = (block: BaseContentBlockModel): string[] => {
  const classes = [`block__${block.type.toLowerCase()}`]
  return classes
}

// Head metadata
useHead(() => {
  const data: IRewriterView = rewriterStore.rewriterData.view
  return {
    title: `${data?.metaData.title} | ${t('magazine.label')}`,
    meta: [
      { name: 'description', content: data?.metaData.description },
      { property: 'author', content: data?.author.name },
      { property: 'og:title', content: `${data?.metaData.title} | ${t('magazine.label')}` },
      { property: 'og:url', content: data?.url },
      { property: 'og:description', content: data?.metaData.description },
      { property: 'og:type', content: 'website' },
      { property: 'og:site_name', content: appData.info.siteId },
      { property: 'og:image', content: data?.mainImageUrl },
      {
        property: 'og:article:published_time',
        content: new Date(data?.date).toLocaleString(appData.info.langCode),
      },
      {
        property: 'og:article:section',
        content: data.categories.length ? data.categories[0].name : '',
      },
      { property: 'og:article:author', content: data.author.name },
      { property: 'og:article:tag', content: data.tags.map(a => a.name) },
    ],
  }
})

// JSON-LD data
const jsonld = computed<WithContext<BreadcrumbList | NewsArticle>[]>(() => {
  const data: IRewriterView = rewriterStore.rewriterData.view
  return [
    {
      '@context': 'https://schema.org',
      '@type': 'NewsArticle',
      'mainEntityOfPage': {
        '@type': 'WebPage',
        '@id': `${appData.externalUrl.eshop}/${data.url}`,
      },
      'headline': data?.metaData.title,
      'description': data?.metaData.description,
      'image': data.mainImageUrl,
      'author': {
        '@type': 'Person',
        'name': data.author.name,
        'url': `${appData.externalUrl.magazine}/${data.author.url}`,
      },
      'datePublished': new Date(data.date).toLocaleString(appData.info.langCode),
      'dateModified': new Date(data.date).toLocaleString(appData.info.langCode),
      'publisher': {
        '@type': 'Organization',
        'name': 'Astratex, s.r.o',
        'logo': {
          '@type': 'ImageObject',
          'url': 'https://www.astratex.cz/Assets/WebDesign/favicons/apple-touch-icon.png',
        },
      },
    },
    {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      'itemListElement': [
        {
          '@type': 'ListItem',
          'position': 1,
          'name': `${t('magazine.label')}`,
          'item': `${appData.externalUrl.magazine}`,
        },
        {
          '@type': 'ListItem',
          'position': 2,
          'name': data.categories.length ? data.categories[0].name : '',
          'item': data.categories.length ? `${appData.externalUrl.magazine}/${data.categories[0].url}` : '',
        },
        {
          '@type': 'ListItem',
          'position': 3,
          'name': data.name,
          'item': `${appData.externalUrl.magazine}/${data.url}`,
        },
      ],
    },
  ]
})

// Expose jsonld for Nuxt to use
defineExpose({
  jsonld,
})
</script>

<style lang="sass">
@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins" as *

.hidden-md-and-up
  @include media-breakpoint-up(md)
    display: none

.hidden-sm-and-down
  @include media-breakpoint-down(sm)
    display: none
</style>
