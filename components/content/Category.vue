<template>
  <LayoutCategoryTemplate>
    <template #menu>
      <AstratexLogo />
      <MenuComponent />
      <FittingLink />
    </template>
    <template #content>
      <TextHeading :align="HeadlineAlignType.Center" :level="1" :text="name" />
      <ArticleCardList />
    </template>
    <template #aside>
      <AvatarGroup />
      <FittingStoreLink />
    </template>
  </LayoutCategoryTemplate>
</template>

<script setup lang="ts">
import { useHead } from '@unhead/vue'
import type { BreadcrumbList, WithContext } from 'schema-dts'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import AstratexLogo from '~/components/AstratexLogo.vue'
import { HeadlineAlignType } from '~/core/constant'
import { useRewriterStore } from '~/stores/rewriter'

const { t } = useI18n()
const rewriterStore = useRewriterStore()
const data = computed(() => rewriterStore.rewriterData.view)

const name = computed(() => {
  return data.value?.metaData.title === '' ? data.value?.name : data.value?.metaData.title
})

useHead({
  title: computed(() => `${data.value.metaData.title} | ${t('magazine.label')}`),
  meta: [
    { name: 'description', content: data.value.metaData.description },
    { property: 'og:title', content: computed(() => `${data.value.metaData.title} | ${t('magazine.label')}`) },
    { property: 'og:url', content: data.value.url },
    { property: 'og:description', content: data.value.metaData.description },
    { property: 'og:type', content: 'website' },
    { property: 'og:site_name', content: rewriterStore.rewriterData.view.metaData.title },
  ],
  script: [
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        'itemListElement': [
          {
            '@type': 'ListItem',
            'position': 1,
            'name': t('magazine.label'),
            'item': rewriterStore.rewriterData.view.metaData.title,
          },
          {
            '@type': 'ListItem',
            'position': 2,
            'name': data.value.name,
            'item': `${rewriterStore.rewriterData.view.metaData.title}/${data.value.url}`,
          },
        ],
      } as WithContext<BreadcrumbList>),
    },
  ],
})
</script>
