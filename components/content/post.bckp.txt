<template>
  <pre>
    {{ JSON.stringify(post.layoutType, null, 2) }}
  </pre>
  <Component :is="layoutComponent">
    <template #headline>
      <!-- <ArticleHeaderComponent v-if="!isVideo" :is-landing-page="isLandingPage" /> -->
      <!-- <VideoHeaderComponent v-else /> -->
    </template>
    <template #content>
      <h1>Hello from template</h1>
      <TextParagraphComponent v-if="!isLandingPage" :text="post.perex" :body-semi="2" class="block__perex" />
      <div v-for="(block, index) in post.contentBlocks" :key="block.order" :class="blockClass(block)">
        <TextHeadingComponent
          v-if="block.type === Block.headline"
          :id="block.htmlId"
          :text="block.text"
          :align="block.headlineAlignType"
          :level="block.headlineType"
        />
        <TextParagraphComponent v-else-if="block.type === Block.text" :text="block.text" />
        <TextPictureComponent v-else-if="block.type === Block.image" :data="block" />
        <!-- <ProductCarousel v-else-if="block.type === Block.productsbycode && block.presentationType === 0" :data="block" /> -->
        <!-- <ProductList v-else-if="block.type === Block.productsbycode && block.presentationType === 1" :data="block" /> -->
        <TextInfoBoxComponent v-else-if="block.type === Block.tip" :data="block" />
        <TextVideoComponent v-else-if="block.type === Block.video && !isVideo && index !== 0" :data="block" />
        <TextPersonInfoComponent v-else-if="block.type === Block.personinfo" :data="block" />
        <TextSeparatorComponent v-else-if="block.type === Block.separator" />
        <TextQuestionAnswerComponent v-else-if="block.type === Block.interview" :data="block" />
        <!-- <ProductCardWrapper v-else-if="block.type === Block.ProductCardWrapper" :block-data="block" /> -->
        <!-- <ProductCard v-else-if="block.type === Block.ProductCardLarge" :block-data="block" card-type="large" /> -->
        <!-- <ProductCard v-else-if="block.type === Block.ProductCardTile" :block-data="block" card-type="tile" /> -->
        <!-- <ImageWithInfo v-if="block.type === Block.ImageWithInfo" :block-data="block" /> -->
        <!-- <ProductCollection v-if="block.type === Block.ProductCollection" :block-data="block" /> -->
        <ArticleImageCarouselComponent v-if="block.type === Block.ImageCarousel" :block-data="block" />
        <TextVideoImageComponent v-if="block.type === Block.VideoImage" :block-data="block" />
        <TextPromoVoucherComponent v-if="block.type === Block.PromoVoucher" :block-data="block" />
      </div>

      <div v-if="onlyForCzSkHuPlRoBg" class="hidden-md-and-up">
        <ArticleSeparatorComponent />
        <TagsGroupComponent />
      </div>
    </template>
    <template #nav>
      <template v-if="onlyForCzSkHuPlRoBg">
        <div class="hidden-sm-and-down">
          <ArticleSeparatorComponent />
          <div class="lg:col-span-20 lg:col-start-3">
            <TagsGroupComponent />
          </div>
        </div>
        <div class="lg:col-span-20 lg:col-start-3">
          <AstratexLogo class="hidden-md-and-up article-logo" />
          <MenuComponent />
        </div>
      </template>
    </template>
    <template #aside>
      <ArticleCardSuggestionListComponent />
    </template>
  </Component>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { SiteId } from '@atx-helper'
import type { WithContext, BreadcrumbList, NewsArticle } from 'schema-dts'
import VideoHeaderComponent from '../article/Video.Header.Component.vue'
import { useHead } from '#imports'

import type { IRewriterView } from '~/core/model/rewrite.model'
import type { BaseContentBlockModel } from '~/core/model/content.model'
import { useRewriterStore } from '~/stores/rewriter'

enum Block {
  headline = 'HEADLINE',
  tip = 'TIP',
  text = 'TEXT',
  image = 'IMAGE',
  productsbycode = 'PRODUCTSBYCODE',
  video = 'VIDEO',
  personinfo = 'PERSONINFO',
  separator = 'SEPARATOR',
  interview = 'QUESTIONANSWER',
  ImageWithInfo = 'IMAGEWITHINFO',
  ProductCardWrapper = 'WRAPPER_PRODUCTCARD',
  ProductCardLarge = 'PRODUCTCARDLARGE',
  ProductCardTile = 'PRODUCTCARDTILE',
  ProductCollection = 'PRODUCTCOLLECTION',
  ImageCarousel = 'IMAGECAROUSEL',
  VideoImage = 'VIDEOIMAGE',
  PromoVoucher = 'PROMOVOUCHER',
}

const rewriterStore = useRewriterStore()
const appData = useStoreAuth()
const { t } = useI18n()

// Computed properties
const post = computed<IRewriterView>(() => rewriterStore.rewriterData.view)
const isVideo = computed<boolean>(() => post.value?.layoutType === 'video')
const isLandingPage = computed<boolean>(() => post.value?.layoutType === 'landingpage')
const layoutComponent = computed(() => {
  if (isLandingPage.value) {
    return resolveComponent('LandingPageTemplate')
  }
  return resolveComponent('LayoutArticleTemplate')
})

const onlyForCzSkHuPlRoBg = computed<boolean>(() => {
  switch (appData.info.siteId) {
    case SiteId.CZ:
    case SiteId.SK:
    case SiteId.HU:
    case SiteId.PL:
    case SiteId.RO:
    case SiteId.BG:
      return true
    default:
      return false
  }
})

// Methods
const blockClass = (block: BaseContentBlockModel): string[] => {
  const classes = [`block__${block.type.toLowerCase()}`]
  return classes
}

// Lifecycle hooks
onMounted(() => {
  if (import.meta.client) {
    // @ts-expect-error - Global ecommerce object
    window.$ecommerce?.pushPageView({ isLandingPage: isLandingPage.value })
  }
})

// Head metadata
useHead(() => {
  const data: IRewriterView = rewriterStore.rewriterData.view
  return {
    title: `${data?.metaData.title} | ${t('magazine.label')}`,
    meta: [
      { name: 'description', content: data?.metaData.description },
      { property: 'author', content: data?.author.name },
      { property: 'og:title', content: `${data?.metaData.title} | ${t('magazine.label')}` },
      { property: 'og:url', content: data?.url },
      { property: 'og:description', content: data?.metaData.description },
      { property: 'og:type', content: 'website' },
      { property: 'og:site_name', content: appData.info.siteId },
      { property: 'og:image', content: data?.mainImageUrl },
      {
        property: 'og:article:published_time',
        content: new Date(data?.date).toLocaleString(appData.info.langCode),
      },
      {
        property: 'og:article:section',
        content: data.categories.length ? data.categories[0].name : '',
      },
      { property: 'og:article:author', content: data.author.name },
      { property: 'og:article:tag', content: data.tags.map(a => a.name) },
    ],
  }
})

// JSON-LD data
const jsonld = computed<WithContext<BreadcrumbList | NewsArticle>[]>(() => {
  const data: IRewriterView = rewriterStore.rewriterData.view
  return [
    {
      '@context': 'https://schema.org',
      '@type': 'NewsArticle',
      'mainEntityOfPage': {
        '@type': 'WebPage',
        '@id': `${appData.externalUrl.eshop}/${data.url}`,
      },
      'headline': data?.metaData.title,
      'description': data?.metaData.description,
      'image': data.mainImageUrl,
      'author': {
        '@type': 'Person',
        'name': data.author.name,
        'url': `${appData.externalUrl.magazine}/${data.author.url}`,
      },
      'datePublished': new Date(data.date).toLocaleString(appData.info.langCode),
      'dateModified': new Date(data.date).toLocaleString(appData.info.langCode),
      'publisher': {
        '@type': 'Organization',
        'name': 'Astratex, s.r.o',
        'logo': {
          '@type': 'ImageObject',
          'url': 'https://www.astratex.cz/Assets/WebDesign/favicons/apple-touch-icon.png',
        },
      },
    },
    {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      'itemListElement': [
        {
          '@type': 'ListItem',
          'position': 1,
          'name': `${t('magazine.label')}`,
          'item': `${appData.externalUrl.magazine}`,
        },
        {
          '@type': 'ListItem',
          'position': 2,
          'name': data.categories.length ? data.categories[0].name : '',
          'item': data.categories.length ? `${appData.externalUrl.magazine}/${data.categories[0].url}` : '',
        },
        {
          '@type': 'ListItem',
          'position': 3,
          'name': data.name,
          'item': `${appData.externalUrl.magazine}/${data.url}`,
        },
      ],
    },
  ]
})

// Expose jsonld for Nuxt to use
defineExpose({
  jsonld,
})
</script>
