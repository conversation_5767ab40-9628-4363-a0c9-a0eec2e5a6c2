<template>
  <nav class="menu body-l">
    <p class="mb-16 caption-caps">
      {{ $t('menu.title') }}
    </p>
    <el-row tag="ul" type="flex">
      <li v-for="(category, index) in menuTree" :key="index">
        <NuxtLink :to="`/${category.url}`">
          {{ category.name }}
        </NuxtLink>
      </li>
    </el-row>
  </nav>
</template>

<script lang="ts" setup>
import type { CategoryTreeModel } from '~/core/model'

const menuTree = ref([]) as Ref<CategoryTreeModel[]>

if (menuTree.value.length === 0) {
  const response = await useMagazineGet<CategoryTreeModel[]>(`/rest/public-content/category/tree?OrderBy=order`)
  if (response && !('statusCode' in response)) {
    menuTree.value = response
  }
}
</script>

<style lang="sass">
@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins" as *

.menu
  margin-bottom: 16px
  flex: 1
  display: flex
  flex-direction: column
  justify-content: center
  align-items: center
  width: 100%
  @include media-breakpoint-up(lg)
    margin-bottom: 24px

  .caption-caps
    text-align: center
    display: block
    text-transform: uppercase

  ul
    margin: 0 8px
    padding: 0
    list-style-type: none
    flex-wrap: wrap

    @include media-breakpoint-up(md)
      flex-direction: column
      align-items: center

  li
    flex-basis: 50%
    padding: 0 8px
    text-align: center

  a
    display: block
    padding: 8px 0
    color: gray
    text-decoration: none
    transition: font-weight 300ms
    &:hover
      font-weight: 700

    @include media-breakpoint-down(md)
      margin-top: -1px
      border-bottom: 1px solid gray
      border-top: 1px solid gray

  .nuxt-link-active
    font-weight: 700

  .article-container &, .landingpage-container &
    ul
      @include media-breakpoint-up(sm)
        flex-direction: row

    a
      margin-top: -1px
      border-bottom: 1px solid gray
      border-top: 1px solid gray
</style>
