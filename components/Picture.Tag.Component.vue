<template>
  <div class="atx-picture" :class="{ 'has-error': hasError, 'is-loading': isLoading }">
    <picture>
      <source v-for="(breakpoint, index) in breakpoints" :key="index" :media="breakpoint" :srcset="resource(rulesets[index])">
      <img :src="path" :alt="alt" :title="alt" draggable="false">
    </picture>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { RULESET } from '~/core/constant'
import { ImageInfo } from '~/core/model'
import { useRuntimeConfig } from '#imports'

defineOptions({
  name: 'PictureTagComponent',
})

const props = defineProps({
  src: {
    type: Object as () => ImageInfo,
    default: () => new ImageInfo(),
    required: true,
  },

  loading: {
    type: String,
    default: 'auto',
  },

  alt: {
    type: String,
    default: '',
  },

  rulesets: {
    type: Array,
    default: () => ['19', '18', '16'],
    required: true,
  },

  breakpoints: {
    type: Array,
    default: () => ['(min-width:992px)', '(min-width:576px)', '(max-width:576px)'],
    required: true,
  },
})

const config = useRuntimeConfig()
const hasError = ref(false)
const isLoading = ref(false)

const path = computed((): string => {
  return resource(`${props.rulesets[0]}`)
})

const resource = (ruleset: string): string => {
  if (props.src.isOnCDN) {
    return `${config.public.cdn}/product/${props.src.image}/${RULESET[ruleset as keyof typeof RULESET]}`
  }

  return `${config.public.imageUrl}/${ruleset}/${props.src.image}`
}

const initLoading = () => {
  const img = new Image()
  img.src = path.value
  isLoading.value = true

  img.onload = () => {
    isLoading.value = false
  }

  img.onerror = () => {
    hasError.value = true
    isLoading.value = false
  }
}

onMounted(() => {
  initLoading()
})
</script>

<style lang="scss" scoped>
.atx-picture {
  vertical-align: middle;
  display: inline-block;
  overflow: hidden;
  position: relative;

  > img {
    vertical-align: middle;
    max-width: 100%;
    height: auto;
    animation: imageload 1000ms;
  }

  &.is-loading {
    background: var(--color-neutrals-200);

    &::before {
      content: '';
      display: block;
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
      background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
      animation: imageloading 1500ms infinite linear;
    }

    > img {
      visibility: hidden;
    }
  }

  &.has-error {
    background: var(--color-neutrals-200);
    > img {
      visibility: hidden;
    }
  }
}
</style>
