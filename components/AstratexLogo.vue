<template>
  <NuxtLink class="logo-main" to="/" :aria-label="$t('magazine.aria')">
    <img :src="logo" :alt="$t('magazine.label')" />
  </NuxtLink>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { SiteId } from '@atx-helper';

// const { $accessor } = useNuxtApp();
const { siteId } = useStoreAuth().info

const logo = computed(() => {
  const domain = SiteId[siteId];
  return domain ? `/logo/AMagazin_logo_${domain.toLowerCase()}.svg` : '/logo/AMagazin_logo_cz.svg';
});
</script>

<style lang="sass">
.logo-main
  text-align: center
  display: block
  min-height: 52px
  width: 100%
  margin: 32px 0
  @media (min-width: 768px)
    margin: 48px 0

  .el-image
    max-width: 200px

.article-logo
  border-top: 1px solid #D9D9DB
  padding: 48px 0
  margin: 0
</style>