<template>
  <span
    class="tag caption-caps"
    :class="data.customColor ? '' : `tag--${data.customCssClass}`"
    :style="data.customColor ? `background: #${data.customColor}` : ''"
  >
    <NuxtLink v-if="link" :to="{ name: 'categories', params: { categories: data.url } }">{{ data.name }}</NuxtLink>
    <template v-else>{{ data.name }}</template>
  </span>
</template>

<script setup lang="ts">
import type { TagBaseModel } from '~/core/model'

defineOptions({
  name: 'TagsComponent',
})

const _props = defineProps({
  data: {
    type: Object as () => TagBaseModel,
    default() {
      return {} as TagBaseModel
    },
  },
  link: {
    type: Boolean,
    default: true,
  },
})
</script>

<style lang="sass">
.tag
  padding: 0 4px
  margin: 4px 0px
  display: inline-block
  color: $paragraph-color

  &--blue
    background: $blue-color
  &--green
    background: $green-color
  &--pink
    background: $pink-color
  &--yellow
    background: $yellow-color
  &--purple
    background: $purple-color
  &--grey
    background: $gray-color
  &--orange
    background: $orange-color

  a
    color: #000
    text-decoration: none

  .tags-group &
    margin-right: 8px
</style>
