<template>
  <div v-if="tags.length > 0" tag="section" class="tags-group">
    <div class="tags-group__caption">
      <TextParagraph :caption-caps="true" :text="$t('tags.title')" />
    </div>
    <TagsComponent
      v-for="(tag, index) in tags"
      :key="index"
      :data="tag"
      :tag="true"
      :data-gtm="`mg-article-label-link-${tag.url}`"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import TagsComponent from './Tags.vue'
import { useRewriterStore } from '~/stores/rewriter'

defineOptions({
  name: 'TagsGroupComponent',
})
const rewriterStore = useRewriterStore()

const tags = computed(() => {
  return rewriterStore.rewriterData.view.tags
})
</script>

<style lang="scss" scoped>
.tags-group {
  margin-bottom: 48px;

  &__caption {
    margin-bottom: 24px;
  }
}
</style>
