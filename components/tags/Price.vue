<template>
  <div tag="span" class="tag-price" :class="`tag--${color}`" :style="customStyle">
    <strong>{{ $currency(price) }}</strong>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

defineOptions({
  name: 'PriceTagComponent',
})

const props = defineProps({
  price: {
    type: Number,
    default: 0,
  },
  bgColor: {
    type: String,
    default: '',
  },
  textColor: {
    type: String,
    default: '',
  },
})

const rewriterStore = useRewriterStore()

const color = computed((): string => {
  return rewriterStore.rewriterData.view.customCssClass || 'pink'
})

const customStyle = computed(() => {
  const styleObject: Record<string, string> = {}

  if (props.bgColor && props.bgColor !== '') {
    styleObject['background-color'] = props.bgColor.startsWith('#') ? props.bgColor : `#${props.bgColor}`
  }

  if (props.textColor && props.textColor !== '') {
    styleObject.color = props.textColor.startsWith('#') ? props.textColor : `#${props.textColor}`
  }

  return styleObject
})
</script>

<style lang="sass" scoped>
.tag-price
  display: inline-block
  border-radius: 3px
  padding: 2px 5px
  color: $paragraph-color
  font-family: $font-family-alternative
  font-size: 11px
  margin-top: ($grid-gutter/2)

  strong
    font-size: 15px
    vertical-align: text-top
</style>
