<template>
  <div
    tag="a"
    :href="`${runtimeConfig.public.eshopUrl}${$t('fitting.cabin.url')}`"
    class="article-card suggestion article-card-list__item"
    target="_blank"
  >
    <div class="fitting-link-suggestion__image article-card__image" type="flex" align="middle" justify="center">
      <img src="~/assets/images/icon-bra.svg" alt="Astratex Poradna v Praze" />
    </div>

    <div class="article-card__content">
      <TagsComponent :data="category as TagBaseModel" :link="false" />
      <TextHeadingComponent :level="6" :text="$t('fitting.cabin.caption')" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive } from 'vue'
import { usePageStore } from '~/stores/page'
import type { TagBaseModel } from '~/core/model'

const runtimeConfig = useRuntimeConfig()
const pageStore = usePageStore()

const { t } = useI18n()

interface PromoItem {
  name: string
  id: number
  creative: string
  position: string
}

const category = reactive({
  name: `${t('fitting.cabin.title')}`,
  customCssClass: 'pink',
})

onMounted(() => {
  const data: PromoItem = {
    name: `${t('fitting.cabin.title')}`,
    id: 1,
    creative: `${t('fitting.cabin.caption')}`,
    position: `mg-${pageStore.currentPageType}-related`,
  }

  const { trackPromoImpressions } = useMagazineGtm()
  trackPromoImpressions([data])
})
</script>

<style lang="scss" scoped>
.fitting-link-suggestion__image {
  background: var(--pink-color);
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 36px;
    @media (min-width: 992px) {
      width: 55px;
    }
  }

  &:hover {
    background: #FFDADF;
  }
}
</style>
