<template>
  <section class="article-card-suggestion-list__side">
    <div v-if="list.length" class="article-card-suggestion-list">
      <TextParagraphComponent :caption-caps="true" :text="$t('article.related.title')" />
      <div v-for="item in list" :key="item.id" class="article-card-list__item">
        <ArticleCardComponent :view="2" :level="6" :data="item" :promo="promo" />
      </div>
      <FittingLinkSuggestionComponent />
    </div>
  </section>
</template>

<script setup lang="ts">
import type { RewriterDataModel } from '~/core/model'
import type { IMagazinPromo } from '~/core/model/ecommerce.model'
import type { PostBaseModel } from '~/core/model/post.model'
import { useRewriterStore } from '~/stores/rewriter'

defineOptions({
  name: 'ArticleCardSuggestionListComponent',
})

const runtimeConfig = useRuntimeConfig()
const rewriterStore = useRewriterStore()
const pageStore = usePageStore()

const list = computed((): PostBaseModel[] => {
  const rewriterData: RewriterDataModel = rewriterStore.rewriterData
  if (!rewriterData || !rewriterData.data || !rewriterData.data.items) {
    return []
  }
  return rewriterData.data.items.filter(i => i.id !== rewriterData.view.id)
})

const promo = computed((): IMagazinPromo[] => {
  return list.value.map((i, index) => ({
    name: i.name,
    id: i.id,
    creative: `${runtimeConfig.public.imageServerUrl}/${i.mainImageUrl}`,
    position: `mg-${pageStore.currentPageType}-related-${index + 1}`,
  }))
})

// Sledování zobrazení
const impressions = () => {
  if (import.meta.client && window.$ecommerce) {
    window.$ecommerce.trackPromoImpressions(promo.value)
  }
}

// Po načtení komponenty
onMounted(() => {
  impressions()
})
</script>

<style lang="scss" scoped>
.article-card-suggestion-list {
  @media (max-width: 992px) {
    border-top: 1px solid var(--divider-color);
    padding-top: 72px;
    padding-bottom: 24px;
  }

  &__side {
    position: sticky;
    top: 20px;
  }

  .caption-caps {
    margin-bottom: 48px;
  }
}
</style>
