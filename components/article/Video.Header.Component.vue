<template>
  <div class="video-header">
    <Heading :text="data.name" :alt="true" :level="1" class="video-header__title" />
    <ArticlePublish />
    <video
      :width="video.width"
      :height="video.height"
      :autoplay="video.autoPlay"
      :controls="video.controls"
      :loop="video.loop"
      :muted="video.muted"
      :poster="video.imageUrl"
    >
      <source :src="video.videoUrl" type="video/mp4" />
      Your browser does not support the video tag.
    </video>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { IRewriterView } from '~/core/model'
import { useRewriterStore } from '~/stores/rewriter'

// Komponenty
import Heading from '~/components/text/Heading.vue'

defineOptions({
  name: 'VideoHeaderComponent',
})

// Store
const rewriterStore = useRewriterStore()

// Computed
const data = computed<IRewriterView>(() => rewriterStore.rewriterData.view)

const video = computed(() => {
  const videoBlock = data.value.contentBlocks.filter(i => i.type === 'VIDEO')[0]
  return videoBlock
})
</script>

<style lang="scss" scoped>
.video-header {
  margin: 0 -24px 48px;

  @media (min-width: 1200px) {
    padding: 0 104px;
    margin: 0 0 72px;
  }

  &__title {
    text-align: center;
    padding-top: 24px;

    @media (min-width: 768px) {
      padding-top: 40px;
    }
  }

  video {
    width: 100%;
  }
}
</style>
