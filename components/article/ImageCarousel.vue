<template>
  <div :id="blockData.htmlId" class="article-carousel-images">
    <carousel v-bind="carouselOptions">
      <slide v-for="(image, index) in blockData.images" :key="index">
        <a v-if="image.linkUrl.length > 0" :href="image.linkUrl" target="_blank" draggable="false">
          <picture draggable="false">
            <source media="(min-width:480px)" :srcset="`${runtimeConfig.public.imageServerUrl}/13/${image.imageUrl}`" />
            <source media="(max-width:479px)" :srcset="`${runtimeConfig.public.imageServerUrl}/12/${image.imageUrl}`" />
            <img
              :src="`${runtimeConfig.public.imageServerUrl}/13/${image.imageUrl}`"
              :alt="image.altText"
              :title="image.title"
              draggable="false"
            />
          </picture>
        </a>

        <picture v-else draggable="false">
          <source media="(min-width:480px)" :srcset="`${runtimeConfig.public.imageServerUrl}/13/${image.imageUrl}`" />
          <source media="(max-width:479px)" :srcset="`${runtimeConfig.public.imageServerUrl}/12/${image.imageUrl}`" />
          <img
            :src="`${runtimeConfig.public.imageServerUrl}/13/${image.imageUrl}`"
            :alt="image.altText"
            :title="image.title"
            draggable="false"
          />
        </picture>
      </slide>
      <template #addons>
        <carousel-navigation />
        <carousel-pagination />
      </template>
    </carousel>
    <div v-if="blockData.link.hasLink" class="text-center article-carousel-images__button hidden-sm-and-up">
      <a
        :href="blockData.link.url"
        :title="blockData.link.title"
        target="_blank"
        class="btn btn-primary rounded-pill"
      >
        {{ blockData.link.text }}
      </a>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRuntimeConfig } from '#imports'
import type { ImageCarouselModel } from '~/core/model/content.model'

defineProps({
  blockData: {
    type: Object as () => ImageCarouselModel,
    required: true,
  },
})

const carouselOptions = {
  itemsToShow: 4,
  itemsToScroll: 1,
  wrapAround: true,
  mouseWheel: true,
  breakpoints: {
    992: { itemsToShow: 3, itemsToScroll: 3 },
    768: { itemsToShow: 2, itemsToScroll: 2 },
  },
}

const runtimeConfig = useRuntimeConfig()
</script>

<style lang="scss" scoped>
.article-carousel-images {
  @media (max-width: 768px) {
    max-width: 343px;
    margin: 0 auto;

    .vueperslides__track-inner {
      display: block;
      white-space: normal;
    }

    .vueperslide {
      padding: 12px;

      &:nth-child(n + 7) {
        display: none;
      }
    }
  }

  &__button {
    margin-top: 48px;
  }
}
</style>
