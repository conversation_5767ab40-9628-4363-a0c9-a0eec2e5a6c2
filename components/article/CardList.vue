<template>
  <div class="article-card-list">
    <div
      v-for="(item, index) in list"
      :key="item.id"
      :class="[
        'article-card-list__item',
        { headline: headline && index === 0 },
      ]"
    >
      <ArticleCard
        :view="headline && index === 0 ? 0 : 1"
        :level="headline && index === 0 ? 3 : 5"
        :data="item"
        :promo="promo"
      />
    </div>
    <div v-if="totalCount > list.length" class="article-card-list__infinite">
      <AtxButtonComponent :type="'primary'" @click="loadMoreArticles">
        {{ $t('button.next_posts') }}
        <template #icon>
          <i class="ri-arrow-right-line"></i>
        </template>
      </AtxButtonComponent>
      <!-- <button class="btn btn-secondary" @click="loadMoreArticles">
        {{ $t('button.next_posts') }}
      </button> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useRuntimeConfig } from '#imports'
import { Pager } from '~/core/constant'
import { RewriterRequest } from '~/core/model'
import type { IMagazinPromo } from '~/core/model/ecommerce.model'
import type { PostBaseModel } from '~/core/model/post.model'
import { useRewriterStore } from '~/stores/rewriter'

defineProps({
  headline: {
    type: Boolean,
    default: false,
  },
})

const route = useRoute()
const runtimeConfig = useRuntimeConfig()
const rewriterStore = useRewriterStore()

const page = ref({
  take: Pager.Take,
  skip: 0,
})
const pager = ref<PostBaseModel[]>([])

const list = computed({
  get: () => pager.value,
  set: (next: PostBaseModel[]) => {
    pager.value = pager.value.concat(next)
  },
})

const totalCount = computed(() => rewriterStore.rewriterData?.data?.totalCount ?? 0)

const promo = computed((): IMagazinPromo[] => {
  return list.value.map((i, index) => ({
    name: i.name,
    id: i.id,
    creative: `${runtimeConfig.public.imageServerUrl}/${i.mainImageUrl}`,
    position: `mg-${route.params.categories}-${index + 1}`,
  }))
})

// Inicializace dat
const initData = async () => {
  await rewriterStore.setRewriterData(new RewriterRequest(route.params.categories as string, page.value))
  const rewriterData = rewriterStore.rewriterData

  if (!rewriterData || !rewriterData.data || !rewriterData.data.items) {
    list.value = []
  }
  else {
    list.value = rewriterData.data.items
  }

  page.value = {
    take: Pager.Take,
    skip: list.value.length,
  }
}

// Načtení dalších článků
const loadMoreArticles = async () => {
  await rewriterStore.setRewriterData(new RewriterRequest(route.params.categories as string, page.value))
  const items = rewriterStore.rewriterData.data.items

  page.value = {
    take: Pager.Take,
    skip: list.value.length + Pager.Skip,
  }

  list.value = items
}
const { trackPromoImpressions } = useMagazineGtm()
// Sledování zobrazení
const impressions = () => {
  trackPromoImpressions(promo.value)
}

await initData()

// Sledování zobrazení pouze na klientovi
onMounted(() => {
  impressions()
})
</script>

<style lang="scss" scoped>
.article-card-list {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-bottom: 48px;

  &__item {
    width: calc(50% - 12px);
    padding-bottom: 48px;

    &.headline {
      width: 100%;
    }

    @media (max-width: 768px) {
      width: 100%;
    }
  }

  .tag {
    margin: 0 4px 12px;
  }

  &__infinite {
    width: 100%;
    text-align: center;
    padding: 48px 24px;
  }

  .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 500;

    &:hover {
      background-color: var(--secondary-color);
    }
  }
}
</style>
