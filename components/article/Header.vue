<template>
  <section class="article-header">
    <div
      class="article-header__picture"
      :class="{ 'has-author': headline.mainImageAuthor !== '' }"
      :data-author="headline.mainImageAuthor"
    >
      <TextVideoImage v-if="fotoVideo !== null" :block-data="fotoVideo" />
      <template v-else-if="headline.url">
        <a :href="headline.url" target="_blank">
          <picture>
            <source media="(min-width:992px)" :srcset="headline.mainImage.source === SourceType.RELATIVE__URL ? `${runtimeConfig.public.imageUrl}/magazin/13/${headline.mainImageUrl}` : headline.mainImage.image" />
            <source media="(min-width:576px)" :srcset="headline.mainImage.source === SourceType.RELATIVE__URL ? `${runtimeConfig.public.imageUrl}/magazin/18/${headline.mainImageUrl}` : headline.mainImage.image" />
            <source media="(max-width:576px)" :srcset="headline.featureImage.source === SourceType.RELATIVE__URL ? `${runtimeConfig.public.imageUrl}/magazin/18/${headline.featureImageUrl}` : headline.featureImage.image" />
            <img
              :src="`${runtimeConfig.public.imageUrl}/magazin/13/${headline.mainImageUrl}`"
              :alt="headline.name"
              :title="headline.name"
            />
          </picture>
        </a>
      </template>
      <template v-else>
        <picture>
          <source media="(min-width:992px)" :srcset="headline.mainImage.source === SourceType.RELATIVE__URL ? `${runtimeConfig.public.imageUrl}/magazin/19/${headline.mainImageUrl}`: headline.mainImage.image" />
          <source media="(min-width:576px)" :srcset="headline.mainImage.source === SourceType.RELATIVE__URL ? `${runtimeConfig.public.imageUrl}/magazin/18/${headline.mainImageUrl}` : headline.mainImage.image" />
          <source media="(max-width:576px)" :srcset="headline.featureImage.source === SourceType.RELATIVE__URL ? `${runtimeConfig.public.imageUrl}/magazin/18/${headline.featureImageUrl}`: headline.featureImage.image" />
          <img
            :src="headline.mainImage.source === SourceType.RELATIVE__URL ? `${runtimeConfig.public.imageUrl}/magazin/13/${headline.mainImageUrl}`: headline.mainImage.image"
            :alt="headline.name"
            :title="headline.name"
          />
        </picture>
      </template>
    </div>
    <ArticleBreadcrumb />
    <TextHeading :text="headline.name" :alt="true" :level="1" class="article-header__title" />
    <TextParagraph
      v-if="isLandingPage"
      :text="headline.perex"
      :body-semi="2"
      tag="p"
      class="article-header__perex"
      :class="{ lp: isLandingPage }"
    />
    <ArticlePublish class="article-header__publish" :class="{ lp: isLandingPage }" />
  </section>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRuntimeConfig } from '#imports'
import { SourceType, type IRewriterView, type VideoImageBlockModel } from '~/core/model'
import { useRewriterStore } from '~/stores/rewriter'

defineProps({
  isLandingPage: {
    type: Boolean,
    default: false,
  },
})

const runtimeConfig = useRuntimeConfig()
const rewriterStore = useRewriterStore()

const headline = computed((): IRewriterView => {
  return rewriterStore.rewriterData.view
})

const fotoVideo = computed((): VideoImageBlockModel | null => {
  return rewriterStore.rewriterData.view.postMainImage
})
</script>

<style lang="scss" scoped>
.article-header {
  display: flex;
  flex-direction: column;

  &__title {
    text-align: center;
    margin-top: 24px;

    @media (max-width: 992px) {
      order: 1;
    }

    .atx-breadcrumb + & {
      margin-top: 0;
    }
  }

  &__perex {
    margin-bottom: 24px;

    @media (min-width: 768px) {
      margin: 0 104px 40px;

      &.lp {
        text-align: center;
      }
    }
  }

  &__picture {
    position: relative;
    margin-bottom: 24px;

    @media (max-width: 992px) {
      margin-left: -24px;
      margin-right: -24px;
    }

    &.has-author::after {
      content: attr(data-author);
      position: absolute;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      color: white;
      padding: 4px 8px;
      font-size: 12px;
    }

    picture {
      display: block;

      img {
        width: 100%;
        display: block;
      }
    }
  }

  &__publish {
    margin-bottom: 24px;
    display: flex;
    align-items: center;justify-content: center;

    &.lp {
      text-align: center;
    }
  }
}
</style>
