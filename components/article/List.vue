<template>
  <el-row tag="main" class="article-list" :gutter="24">
    <el-col
      v-for="(rewriter, index) in list" :key="rewriter.id"
      :md="props.headline && index === 0 ? 24 : 12"
      class="article-card-list__item"
    >
      <ArticleCard :view="index === 0 ? 0 : 1" :level="index === 0 ? 3 : 5" :data="rewriter" />
    </el-col>
    <el-col v-if="list.length < totalCount" class="article-card-list__infinite">
      <el-button type="primary" round @click="loadMore">
        {{ $t('button.next_posts') }}
      </el-button>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { Pager } from '~/core/constant'
import type { PostBaseModel } from '~/core/model'

const props = defineProps<{
  headline: boolean
}>()

const rewriterStore = useRewriterStore()
// const rewriters = computed(() => rewriterStore.rewriterData.data?.items ?? [])
const totalCount = computed(() => rewriterStore.rewriterData?.data?.totalCount ?? 0)

const page = ref({
  take: Pager.Take,
  skip: Pager.Take,
})

const pager = ref<PostBaseModel[]>([])

const list = computed({
  get: () => pager.value,
  set: (next: PostBaseModel[]) => {
    pager.value = pager.value.concat(next)
  },
})

const loadMore = async () => {
  await rewriterStore.setRewriterData({
    url: rewriterStore.rewriterData.view.url,
    query: {
      ...page.value,
    },
  })
  const items = rewriterStore.rewriterData.data?.items ?? []
  list.value = items
  page.value.skip += page.value.take
}
await rewriterStore.fetchRewriters()
pager.value = rewriterStore.rewriterData.data?.items ?? []
</script>

<style>

</style>
