<template>
  <nav v-if="view.hasBreadcrumbs" class="atx-breadcrumb">
    <ol class="caption">
      <li>
        <NuxtLink to="/">
          <span>{{ $t('magazine.title') }}</span>
        </NuxtLink>
      </li>
      <li v-if="category.name">
        <NuxtLink :to="category.url">
          <span>{{ category.name }}</span>
        </NuxtLink>
      </li>
      <li v-html="article" />
    </ol>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRewriterStore } from '~/stores/rewriter'
import { CategoryBaseModel, type IRewriterView } from '~/core/model'

defineOptions({
  name: 'BreadcrumbComponent',
})

const rewriterStore = useRewriterStore()

const view = computed((): IRewriterView => {
  return rewriterStore.rewriterData.view
})

const category = computed((): CategoryBaseModel => {
  const categories: CategoryBaseModel[] = view.value.categories
  if (!categories.length) {
    return new CategoryBaseModel()
  }

  return categories[0]
})

const article = computed((): string => {
  return view.value.name
})
</script>

<style lang="scss" scoped>
.atx-breadcrumb {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 30px;
    background: linear-gradient(270deg, #fff, transparent);
  }

  ol {
    padding: 24px 0;
    margin: 0;
    display: flex;
    white-space: nowrap;
    overflow: auto;
    list-style-type: none;
  }

  li {
    padding: 0 0 0 8px;

    & + li {
      &::before {
        font-family: 'icomoon';
        content: '\e914';
        font-size: 8px;
        margin-right: 8px;
      }
    }
  }

  a {
    text-decoration: none;
  }
}
</style>
