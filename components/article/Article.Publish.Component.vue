<template>
  <el-row v-if="publish.hasPostAuthor" align="middle" class="article-publish">
    <el-row class="article-publish__date" type="flex" justify="end">
      <el-row class="article-publish__date--inner">
        <ParagraphComponent :text="$dateMonthLong(publish.date)" :body-semi="3" />
        <TagsComponent
          v-for="(category, index) in publish.categories"
          :key="index"
          :data="category"
          :data-gtm="`mg-article-category-link-${category.url}`"
        />
      </el-row>
    </el-row>
    <el-row class="article-publish__author">
      <AvatarComponent :author="publish.author" :data-gtm="`mg-article-author-link-${publish.author.url}`" />
    </el-row>
  </el-row>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import AvatarComponent from '../avatar/Avatar.Component.vue'
import TagsComponent from '../tags/Tags.Component.vue'
import ParagraphComponent from '../text/Paragraph.Component.vue'
import { type } from '../../.nuxt/types/imports'
import { useRewriterStore } from '~/stores/rewriter'

defineOptions({
  name: 'ArticlePublishComponent',
})

const rewriterStore = useRewriterStore()

const publish = computed(() => {
  return rewriterStore.rewriterData.view
})
</script>

<style lang="sass">
@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins" as *
@use '_variables'

.article-publish
  margin-bottom: 26px
  @include media-breakpoint-up(md)
    margin-bottom: 40px

  &::before
    content: ''
    width: 1px
    height: 48px
    display: block
    position: absolute
    left: 50%
    top: 50%
    background: $divider-color
    transform: translate(-50%, -50%)

  &__date, &__author
    flex: 1

    @include media-breakpoint-up(md)
      padding: 0 ($grid-gutter * 2)

  &__date
    text-align: center

    &--inner
      flex-direction: column
      @include media-breakpoint-down(md)
        padding: 0 $grid-gutter

  .tag
    margin: 0 4px
</style>
