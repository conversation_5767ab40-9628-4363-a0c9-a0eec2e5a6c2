<template>
  <div class="article-card-list">
    <div
      v-for="(item, index) in list"
      :key="item.id"
      :class="[
        'article-card-list__item',
        { headline: headline && index === 0 },
      ]"
    >
      <ArticleCardComponent
        :view="headline && index === 0 ? 0 : 1"
        :level="headline && index === 0 ? 3 : 5"
        :data="item"
        :promo="promo"
      />
    </div>
    <div v-if="list.length < totalCount" class="article-card-list__infinite">
      <button class="btn btn-primary" @click="loadMoreArticles">
        {{ $t('button.next_posts') }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import ArticleCardComponent from './Article.Card.Component.vue'
import { useRuntimeConfig } from '#imports'
import { Pager } from '~/core/constant'
import type { PostBaseModel } from '~/core/model/post.model'
import type { IMagazinPromo } from '~/core/model/ecommerce.model'
import { useRewriterStore } from '~/stores/rewriter'
import { RewriterRequest } from '~/core/model'

defineOptions({
  name: 'ArticleCardListComponent',
})

const props = defineProps({
  headline: {
    type: Boolean,
    default: false,
  },
})

const route = useRoute()
const runtimeConfig = useRuntimeConfig()
const rewriterStore = useRewriterStore()

const pager = ref<PostBaseModel[]>([])
const page = ref({
  take: Pager.Take,
  skip: 0,
})

const list = computed({
  get: () => pager.value,
  set: (next: PostBaseModel[]) => {
    pager.value = [...pager.value, ...next]
  },
})

const totalCount = computed(() => rewriterStore.rewriterData?.data?.totalCount ?? 0)

const promo = computed((): IMagazinPromo[] => {
  return list.value.map((i, index) => ({
    name: i.name,
    id: i.id,
    creative: `${runtimeConfig.public.imageServerUrl}/${i.mainImageUrl}`,
    position: `mg-${route.params.categories}-${index + 1}`,
  }))
})

// Inicializace dat
const initData = () => {
  const rewriterData = rewriterStore.rewriterData
  if (!rewriterData || !rewriterData.data || !rewriterData.data.items) {
    list.value = []
  }
  else {
    list.value = rewriterData.data.items
  }

  page.value = {
    take: Pager.Take,
    skip: list.value.length,
  }
}

// Načtení dalších článků
const loadMoreArticles = async () => {
  await rewriterStore.setRewriterData(new RewriterRequest(route.params.categories as string, page.value))
  const items = rewriterStore.rewriterData.data.items

  page.value = {
    take: Pager.Take,
    skip: list.value.length + Pager.Skip,
  }

  list.value = items
}

// Sledování zobrazení
const impressions = () => {
  if (import.meta.client && window.$ecommerce) {
    window.$ecommerce.trackPromoImpressions(promo.value)
  }
}

// Inicializace
initData()

// Po načtení komponenty
onMounted(() => {
  impressions()
})
</script>

<style lang="scss" scoped>
.article-card-list {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-bottom: 48px;

  &__item {
    width: calc(50% - 12px);
    padding-bottom: 48px;

    &.headline {
      width: 100%;
    }

    @media (max-width: 768px) {
      width: 100%;
    }
  }

  .tag {
    margin: 0 4px 12px;
  }

  &__infinite {
    width: 100%;
    text-align: center;
    padding: 48px 24px;
  }

  .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 500;

    &:hover {
      background-color: var(--secondary-color);
    }
  }
}
</style>
