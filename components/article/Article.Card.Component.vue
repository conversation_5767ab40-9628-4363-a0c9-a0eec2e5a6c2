<template>
  <NuxtLink
    :to="{ name: 'categories', params: { categories: data.url } }"
    class="article-card"
    :class="{ suggestion: CardType.Suggestion === view, video: data.layoutType === 'video' }"
    @click="trackPromoClick"
  >
    <div
      class="article-card__image"
      :class="{ empty: !imageRatio.main }"
      :style="`background-image: url(\'${CardType.Hero === view ? imageRatio.main : imageRatio.feature}\')`"
    />
    <el-row class="article-card__content">
      <TagsComponent v-for="(category, index) in data.categories" :key="index" :data="category" :link="false" />
      <TextHeadingComponent :level="level" :alternative="alternative" :text="data.name" />
      <TextParagraphComponent
        v-if="CardType.Suggestion !== view"
        :body="3"
        :text="$dateMonthLong(data.date)"
        class="text-info"
      />
    </el-row>
  </NuxtLink>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import TagsComponent from '~/components/tags/Tags.Component.vue'
import { useRuntimeConfig } from '#imports'
import type { PostBaseModel } from '~/core/model/post.model'
import { SourceType } from '~/core/model/post.model'
import type { IMagazinPromo } from '~/core/model/ecommerce.model'

defineOptions({
  name: 'ArticleCardComponent',
})

enum CardType {
  Hero,
  List,
  Suggestion,
}

const props = defineProps({
  view: {
    type: Number,
    default: 0,
  },
  level: {
    type: Number,
    default: 3,
  },
  data: {
    type: Object as () => PostBaseModel,
    required: true,
  },
  alternative: {
    type: Boolean,
    default: true,
  },
  promo: {
    type: Array as () => IMagazinPromo[],
    default: () => [],
  },
})

const runtimeConfig = useRuntimeConfig()

const imageSize = computed((): string => {
  if (CardType.Suggestion === props.view) {
    return 't'
  }
  else if (CardType.Hero === props.view) {
    return 'l'
  }

  return 's'
})

const imageRatio = computed(() => {
  const main = props.data.mainImage?.source === SourceType.RELATIVE__URL
    ? `${runtimeConfig.public.imageUrl}/magazin/13/${props.data.mainImageUrl}`
    : props.data.mainImageUrl

  const feature = props.data.mainImage?.source === SourceType.RELATIVE__URL
    ? `${runtimeConfig.public.imageUrl}/magazin/18/${props.data.featureImageUrl}`
    : props.data.featureImageUrl

  return {
    main,
    feature,
  }
})

const trackPromoClick = () => {
  if (import.meta.client && window.$ecommerce) {
    window.$ecommerce.trackPromoClicks(props.promo.filter(i => i.id === props.data.id))
  }
}
</script>

<style lang="sass">
@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins" as *

.article-card
  text-align: center
  flex-direction: column
  text-decoration: none
  display: flex
  &__content
    flex-direction: column
    align-items: center
    justify-content: center
  &:not(.suggestion) &__content
    padding: 0 $grid-gutter

  &.suggestion
    @include media-breakpoint-up('md')
      text-align: left

    @include media-breakpoint-between('md', 'lg')
      flex-direction: row

    @include media-breakpoint-down('lg')
      align-items: center

    .tag
      margin: 0px 8px 8px 0

  &.suggestion &__content
    @include media-breakpoint-down('lg')
      padding: 0 20px
      flex: 1

  &__image
    background-position: center
    background-size: cover
    background-repeat: no-repeat
    border-radius: 8px
    margin-bottom: 12px
    aspect-ratio: 3/2

    @include media-breakpoint-up('lg')
      .headline &
        aspect-ratio: 21/9

    &.empty
      background: #f2f2f2

      &::before
        content: 'no image'
        position: absolute
        padding: 10px

  &.suggestion &__image
    margin-bottom: 8px
    min-width: 220px

    @include media-breakpoint-down('md')
      min-width: 320px

  picture img
    border-radius: 8px

  &.video
    picture
      position: relative
      &::before
        content: ''
        background: #fff url('~/assets/images/play.svg') 50% no-repeat
        display: block
        position: absolute
        width: 40px
        height: 40px
        border: 2px solid #fff
        border-radius: 50%
        left: $grid-gutter
        bottom: $grid-gutter
        transition: background 300ms
        box-shadow: 0px 0px 7px 3px rgba(0, 0, 0, 0.08)

      &:hover
        &::before
          background: $secondary-color url('~/assets/images/play-hover.svg') 50% no-repeat

    &.suggestion
      picture
        &::before
          width: 32px
          height: 32px
</style>
