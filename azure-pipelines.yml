name: Dev pipeline

trigger:
    branches:
        include:
            - dev
            - main

resources:
    repositories:
        - repository: InfraPipelineTemplates
          type: git
          name: E-Shop-Infrastructure/PipelineTemplates
          ref: main

variables:
    - group: TextForJson
    - name: tag
      value: '$(Build.BuildId)'
    - name: env_file
      value: 'env/.env.$(app_env)'

stages:
    - template: templates/nuxt_app_dev_build_and_deploy.yaml@InfraPipelineTemplates
      parameters:
          dockerRepository: 'eshop-frontend/magazine'
          syncTexts: false
          port: 3055
          appName: 'magazine'
