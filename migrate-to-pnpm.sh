#!/bin/bash

# Skript pro migraci z Yarn na pnpm
echo "Migrace z Yarn na pnpm..."

# Kontrola, zda je pnpm nainstalován
if ! command -v pnpm &> /dev/null; then
    echo "pnpm není nainstalován. Instaluji..."
    npm install -g pnpm
fi

# Odstranění yarn.lock
echo "Odstraňuji yarn.lock..."
rm -f yarn.lock

# Odstranění node_modules
echo "Odstraňuji node_modules..."
rm -rf node_modules

# Instalace závislostí pomocí pnpm
echo "Instaluji závislosti pomocí pnpm..."
pnpm install

# Aktualizace package.json - nahrazení yarn příkazů za pnpm
echo "Aktualizuji package.json..."
sed -i '' 's/"yarn /"pnpm /g' package.json

echo "Migrace dokončena. Nyní mů<PERSON> používat pnpm místo yarn."
echo "Spusť 'pnpm dev' pro spuštění vývojového serveru."
