{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "rundev": "pnpm install && nuxt dev"}, "dependencies": {"@atx-eshop/eshop-components": "*********", "nuxt": "3.17.5", "nuxt-build-cache": "^0.1.1", "schema-dts": "^1.1.5", "vue-router": "^4.4.5", "vue3-carousel-nuxt": "1.1.6"}, "devDependencies": {"eslint-import-resolver-node": "^0.3.9", "nuxi": "3.17.0"}, "packageManager": "pnpm@10.12.1+sha512.f0dda8580f0ee9481c5c79a1d927b9164f2c478e90992ad268bbb2465a736984391d6333d2c327913578b2804af33474ca554ba29c04a8b13060a717675ae3ac"}