name: Release pipeline

parameters:
    - name: deploy_to_production
      displayName: Deploy to production
      type: boolean
      default: false
    - name: deploy_to_backup
      displayName: Deploy to backup
      type: boolean
      default: false

trigger:
    branches:
        include:
            - release/*

resources:
    repositories:
        - repository: InfraPipelineTemplates
          type: git
          name: E-Shop-Infrastructure/PipelineTemplates
          ref: main

stages:
    - template: templates/nuxt_app_staging_build_and_deploy.yaml@InfraPipelineTemplates
      parameters:
          dockerRepository: 'eshop-frontend/magazine'
          syncTexts: false
          port: 3150
          productionPort: 3050
          appName: 'magazine'
          buildProductionImage: true
          deployProductionImage: ${{ parameters.deploy_to_production }}
          deployToBackup: ${{ parameters.deploy_to_backup }}
