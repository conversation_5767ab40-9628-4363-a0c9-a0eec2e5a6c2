import { i18nlocales } from '@atx-helper'

export default defineI18nLocale((locale: string) => {
  const i18nLocale = i18nlocales.find(x => x.code === locale)
  if (!i18nLocale) {
    console.error('Invalid locale requested or not registered.')
    return {}
  }

  const fetchUrl = import.meta.server
    ? (process.env.NUXT_PUBLIC_TRANSLATION_BASE_URL as string)
    : (useRuntimeConfig().public.translationBaseUrl)
  const nuxtBranch = import.meta.server
    ? (process.env.NUXT_NUXT_BRANCH as string)
    : (useRuntimeConfig().public.nuxtBranch)

  let environment = 'Production'
  if (nuxtBranch === 'dev' || nuxtBranch === 'local') {
    environment = 'Development'
  }
  else if (nuxtBranch === 'release') {
    environment = 'Staging'
  }

  try {
    return $fetch(fetchUrl, {
      headers: {
        'X-Localization': i18nLocale.iso,
        'X-Environment': environment,
        'X-Application': import.meta.server
          ? (process.env.NUXT_PUBLIC_APLICATION_TYPE as string)
          : useRuntimeConfig().public.applicationType,
        'X-Gateway-Key': import.meta.server
          ? (process.env.NUXT_PUBLIC_GATEWAY_KEY as string)
          : useRuntimeConfig().public.gatewayKey,
      },
    })
  }
  catch (error) {
    console.error(error)
    return {}
  }
})
