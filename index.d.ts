declare module 'nuxt/app' {
  interface NuxtApp {
    $date(msg: string | Date): string
    $currency(msg: number): string
    $dateMonthLong(msg: string | Date): string
    $dateMonth(msg: string | Date): string
    $time(msg: string | Date): string
    $creditCurrency(msg: number): string
    $creditsWithoutCurrency(msg: number): number
    $formatBirthday(msg: string | Date): string
    $gtm: GtmInterface
    $rewriterApi: FetchInstance
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    $date(msg: string | Date): string
    $currency(msg: number): string
    $dateMonthLong(msg: string | Date): string
    $time(msg: string | Date): string
    $creditCurrency(msg: number): string
    $creditsWithoutCurrency(msg: number): number
    $gtm: GtmInterface
    $rewriterApi: FetchInstance
  }
}

declare function useNuxtApp(): NuxtApp

export {}

declare module 'vueperslides';
