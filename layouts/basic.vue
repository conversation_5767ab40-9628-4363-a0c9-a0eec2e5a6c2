<template>
  <el-row class="main__container">
    <el-row v-if="loading">
      {{ loadingText }}
    </el-row>
    <el-row :gutter="32" class="category-main">
      <el-col :xs="24" :sm="8" :lg="6" class="category-main__menu">
        <slot name="menu" />
      </el-col>
      <el-col :xs="24" :sm="16" :lg="18" class="category-main__content">
        <slot name="content" />
      </el-col>
      <el-col :xs="24" :sm="8" :lg="6" class="category-main__aside">
        <slot name="aside" />
      </el-col>
    </el-row>
  </el-row>
</template>

<script setup lang="ts">
const { loading, loadingText } = useLoadingStore()
</script>

<style lang="sass">
@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins" as *

.loading
  position: fixed
  inset: 0
  background: rgba(0, 0, 0, 0.9)
  backdrop-filter: blur(4px)
  z-index: 1000
  color: white
  align-items: center
.category-main
  display: block
  width: 100%
  max-width: 1280px
  margin: 0 auto
  &__menu
    float: left
    width: 100%
    display: flex
    justify-content: center
    align-items: center
  &__content
    float: right
    padding-top: ($grid-gutter * 2)
    @include media-breakpoint-up(lg)
      padding-top: ($grid-gutter * 3)
  &__aside
    clear: left
</style>
