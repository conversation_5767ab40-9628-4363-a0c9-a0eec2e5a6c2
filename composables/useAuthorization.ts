import type { AppAuthResponse } from '@atx-model'

export default async function (): Promise<AppAuthResponse | false> {
  const config = useRuntimeConfig()
  const site = useSiteLocation()

  try {
    // console.log(`auth/auth request to: ${site.url.eshop}`)
    // console.log(`storeAuth.site.localization: ${site.localization}`)
    console.log(site.url.eshop, site.localization)
    const { data, error } = await useFetch<AppAuthResponse>('/v1/auth/auth', {
      method: 'POST',
      baseURL: site.url.eshop,
      headers: {
        'X-Localization': site.localization,
        'X-Application': process.env.NUXT_PUBLIC_APLICATION_TYPE ?? '',
        'X-Forwarded-For': useRequestHeaders()['x-forwarded-for'],
      },
      body: {
        key: config.apiKey,
        secret: config.apiSecret,
      },
    })

    if (error.value) {
      console.error(error.value)
      return false
    }

    if (data.value) {
      return data.value
    }
    else {
      console.error('data failed')

      return false
    }
  }
  catch (e) {
    console.info('app authorization faield')
    console.error(e)
    return false
  }
}
