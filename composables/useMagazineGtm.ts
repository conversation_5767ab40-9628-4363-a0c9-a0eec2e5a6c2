import type { IMagazinPromo, IProductPromo } from '~/core/model/ecommerce.model'

let gtm = null
if (import.meta.client) {
  gtm = useGtm()
}

export const useMagazineGtm = () => {
  const trackPromoImpressions = (articles: IMagazinPromo[]) => {
    if (!gtm) return
    gtm.push({
      event: 'promotionImpression',
      ecommerce: {
        promoView: {
          promotions: articles,
        },
      },
    })
  }
  const trackPromoClicks = (articles: IMagazinPromo[]) => {
    if (!gtm) return
    gtm.push({
      event: 'promotionClick',
      ecommerce: {
        promoClick: {
          promotions: articles,
        },
      },
    })
  }
  const trackProductImpressions = (products: IProductPromo[]) => {
    if (!gtm) return
    const { info } = useStoreAuth()
    gtm.push({
      event: 'productImpression',
      ecommerce: {
        currencyCode: info.currency,
        impressions: products,
      },
    })
  }
  const trackProductClick = (products: IProductPromo[]) => {
    if (!gtm) return
    gtm.push({
      event: 'productClick',
      ecommerce: {
        click: {
          products,
        },
      },
    })
  }

  return { trackPromoImpressions, trackPromoClicks, trackProductImpressions, trackProductClick }
}
