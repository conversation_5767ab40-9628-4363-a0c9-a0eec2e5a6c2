@use "@/node_modules/@atx-eshop/eshop-components/src/assets/sass/_mixins" as *
@use '@/assets/sass/_variables' as *

picture
  display: block

  img
    max-width: 100%
    height: auto
    display: inline-block
    vertical-align: middle
    border-radius: 16px

hr
  border: none
  border-top: 1px solid
  border-color: $divider-color

.line-divider
  margin: ($grid-gutter * 2) 0

  @include media-breakpoint-up(md)
    margin: ($grid-gutter * 3) ($grid-gutter + calc($grid-gutter / 2))

.flex-column-center
  display: flex
  flex-direction: column
  justify-content: center
  align-items: center

.block__text
  ul
    margin: 0
    padding: 3px 27px

  li
    margin: 4px 0
    @extend .body-n

.vueperslides__parallax-wrapper
  padding-bottom: 0!important

.vueperslide
  display: inline-block
  white-space: normal
  flex-shrink: 0
  padding: 0 8px
  vertical-align: top
  width: 100%

  @include media-breakpoint-up(md)
    padding: 0 9px

    &--clone-1
      position: absolute
      top: 0
      bottom: 0
      right: 0
      left: 0
      z-index: -1

.vueperslides
  overflow: hidden
  @include media-breakpoint-up(md)
    padding: 0 24px

  @include media-breakpoint-up(lg)
    padding: 0 96px

  .gutter-side &__track
    @include media-breakpoint-down(md)
      padding-right: 64px

  .thin-slider &
    @include media-breakpoint-up(lg)
      padding: 0 198px

  &__track
    overflow: hidden

    &-inner
      white-space: nowrap
      display: flex
      position: relative
      margin: 0 -8px

      @include media-breakpoint-up(md)
        margin: 0 -9px

      .thin-slider &
        justify-content: center

  &__arrow
    position: absolute
    top: 50%
    border: none
    background: none
    outline: none
    width: 48px
    height: 48px
    box-shadow: 0px 0px 7px 3px rgba(0, 0, 0, 0.08)
    transform: translate(0, -50%)
    background: #fff
    cursor: pointer
    border-radius: 50%
    transition: background 200ms

    &:hover
      background: #F72F57

      svg
        stroke: #fff

    &--prev
      left: 0

    &--next
      right: 0

    svg
      vertical-align: middle
      stroke: currentColor
      fill: none
      height: 20px
