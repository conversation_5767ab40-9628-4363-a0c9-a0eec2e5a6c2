import { defineStore } from 'pinia'

interface LoadingState {
  loading: boolean
  loadingText: string
}

export const useLoadingStore = defineStore('loadingStore', {
  state: (): LoadingState => ({
    loading: true,
    loadingText: '',
  }),
  actions: {
    setLoading(loading: boolean) {
      this.loading = loading
    },
    setLoadingText(text: string) {
      this.loadingText = text
    },
  },
})
