import type { ExtendedAddress, ICampaign, IClientCredits, IClientCreditsOverview, ICreditActivationModel, ICredits, IUser } from '@/core/model'

export const useClientData = defineStore('client', {
  state: () => ({
    addresses: [] as ExtendedAddress[],
    user: {} as IUser,
    campaign: {} as ICampaign,
    creditsData: {} as ICredits,
    paging: {
      length: 4,
      page: 1,
    },
    latestZipCodeRestriction: '',
    isError: false
  }),
  actions: {
    async getClientUser() {
      if (!this.user.loaded) {
        const response = await useGetFetch<IUser>('/v1/client/user')

        if (!response || 'statusCode' in response) {
          this.user = { ...{}, loaded: false } as IUser

          return
        }

        this.user = { ...response, loaded: true }
      }
    },

    async getClientCampaign() {
      if (!this.campaign.loaded) {
        const response = await useGetFetch<ICampaign>('/v1/client/campaign')

        if (!response || 'statusCode' in response) {
          this.campaign = {} as ICampaign

          return
        }

        this.campaign = { ...response, loaded: true }
      }
    },
    async toggleClientCampaing(news: ICampaign) {
      await usePutFetch('/v1/client/campaign', news)
      this.campaign = { ...news, loaded: true }
    },

    async getClientCredits() {
      try {
      const response = await useGetFetch<IClientCredits>('/v1/client/credits')

      if (!response || 'statusCode' in response) {
        this.creditsData = {} as ICredits

        return
      }

      this.creditsData.credits = response
      } catch (error) {
        this.isError = true;
      }
    },

    async getClientCreditsOverview(page?: number, length?: number) {
      try {
      const reposnse = await useGetFetch<IClientCreditsOverview>(`/v1/client/credits/overview?length=${length ?? 12}&page=${page ?? 1}`)

      if (!reposnse || 'statusCode' in reposnse) {
        this.creditsData.overview = {} as IClientCreditsOverview

        return
      }

      this.creditsData.overview = reposnse
      } catch (error) {
        this.isError = true;
      }
    },

    async getClientCreditsActivation() {

      try {
        
      const response = await useGetFetch<ICreditActivationModel[]>('/v1/client/credits/activation')

      if (!response || 'statusCode' in response) {
        this.creditsData.activation = []

        return
      }

      this.creditsData.activation = response
      } catch (error) {
        this.isError = true;
      }
    },
    setClientUserLoaded(val: boolean) {
      this.user.loaded = val
      this.campaign.loaded = val
      this.addresses = []
    },
  },
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useClientData, import.meta.hot))
}
