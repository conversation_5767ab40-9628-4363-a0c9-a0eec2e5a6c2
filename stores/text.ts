import type { IBenefits } from '@atx-model'

export const useTextData = defineStore('text', {
  state: () => ({
    dashBenefits: [] as IBenefits[],
  }),

  actions: {
    async getDashBenefits() {
      if (!this.dashBenefits.length) {
        const response = await useGetFetch<IBenefits[]>('/v1/clubs/benefits')

        if (!response || 'statusCode' in response) return []

        this.dashBenefits = response
      }
    },
  },
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useTextData, import.meta.hot))
}
