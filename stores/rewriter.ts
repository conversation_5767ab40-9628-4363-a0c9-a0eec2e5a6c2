import { defineStore } from 'pinia'
import type { ErrorResponseBody } from '~/core/model'
import type { RewriterDataModel, RewriterModel, RewriterRequest } from '~/core/model/rewrite.model'

interface AuthorState {
  rewriter: RewriterModel
  loading: boolean
  rewriterData: RewriterDataModel
}

export const useRewriterStore = defineStore('rewriter', {
  state: (): AuthorState => ({
    rewriter: {} as RewriterModel,
    loading: true,
    rewriterData: {} as RewriterDataModel,
  }),

  actions: {
    async setRewriterData(request: RewriterRequest) {
      this.loading = true
      const query = useUseQueryString(request)

      try {
        const response = await useMagazineGet<RewriterDataModel>(`/rest/rewriter/data${query}`)

        if (response && 'statusCode' in response) {
          return response as ErrorResponseBody
        }
        else {
          this.rewriterData = response
        }
      }
      catch (error) {
        console.error('Chyba při načítání autorů:', error)
        // V případě chyby použijeme testovací data
        this.rewriterData = {} as RewriterDataModel
      }
      finally {
        this.loading = false
      }
    },
    async fetchRewriters() {
      this.loading = true

      try {
        const response = await useMagazineGet<RewriterDataModel>(`/rest/rewriter/data`)

        if (response && 'statusCode' in response) {
          return response as ErrorResponseBody
        }
        else {
          this.rewriterData = response
        }
      }
      catch (error) {
        console.error('Chyba při načítání autorů:', error)
        // V případě chyby použijeme testovací data
        this.rewriterData = {} as RewriterDataModel
      }
      finally {
        this.loading = false
      }
    },
  },
})
