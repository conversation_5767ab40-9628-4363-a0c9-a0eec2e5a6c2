import type { IBrafittingInfo } from '@atx-model/brafittinginfo.model'
import type { ITerms } from '@/core/model'

export const useTerms = defineStore('terms', {
  state: () => ({
    terms: [] as ITerms[],
    measures: {} as IBrafittingInfo,
  }),

  actions: {
    async getUserTerms(force: boolean = false) {
      if (!this.terms.length || force) {
        const response = await useGetFetch<ITerms[]>('/v1/brafitting/terms')

        if (!response || 'statusCode' in response) {
          this.terms = []

          return
        }

        this.terms = response
      }
    },

    async getUserMeasures() {
      if (!this.measures.latestMeasures) {
        const response = await useGetFetch<IBrafittingInfo>('/v1/brafitting/user-info')

        if (!response || 'statusCode' in response) {
          this.measures = {} as IBrafittingInfo

          return
        }

        this.measures = response
      }
    },
  },
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useTerms, import.meta.hot))
}
