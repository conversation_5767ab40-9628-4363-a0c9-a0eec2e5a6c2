import { defineStore } from 'pinia'
import type { AuthorBaseModel, AuthorViewModel, ErrorResponseBody } from '~/core/model'

interface AuthorState {
  authors: AuthorBaseModel[]
}

export const useAuthorStore = defineStore('author', {
  state: (): AuthorState => ({
    authors: [],
  }),

  actions: {
    async fetchAuthors() {
      const { setLoading } = useLoadingStore()
      setLoading(true)
      try {
        const response = await useMagazineGet<AuthorViewModel[]>(`/rest/public-content/author/?hasPublicPosts=true`)

        if (!response || 'statusCode' in response) {
          console.log('Chyba při načítání autorů:', response)
          return response as ErrorResponseBody
        }
        else {
          this.authors = response
        }
      }
      catch (error) {
        console.error('Chyba při načítání autorů:', error)
        this.authors = []
      }
      finally {
        setLoading(false)
      }
    },
  },
})
