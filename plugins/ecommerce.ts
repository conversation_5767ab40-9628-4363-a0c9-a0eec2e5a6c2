import { debounce } from '@atx-eshop/astratex-ui/core/helper'
import type { IGTMModule } from '@atx-eshop/astratex-ui/core/model'
import type { IDataLayerEvent, IPageViewEvent } from '@atx-eshop/astratex-ui/core/model/ecommerce.model'
import type { ClientData, SiteInfo } from '@atx-eshop/astratex-ui/core/model/appAuth.model'
import { createPageViewEvent } from '@atx-eshop/astratex-ui/core/ecommerce'
import type { Address } from '@atx-eshop/astratex-ui/core/model/address.model'
import type { StoreAccessorType } from '~/store'
import type { IMagazinPromo, IProductPromo } from '~/core/model/ecommerce.model'

export interface IDataLayerEventV2 extends IDataLayerEvent {
  eventCallback?: Function
  eventTimeout?: number
}

type EventCallback = ((containerId: string) => void) | null

interface IAuthStoreState {
  loggedIn: boolean
  user?: ClientData
}

/** Plugin for ecommerce data. */
export class EcommercePlugin {
  #gtmModule: IGTMModule
  #accessor: StoreAccessorType
  #authStoreState: IAuthStoreState

  constructor(gtmModule: any, store: StoreAccessorType, authStoreState: IAuthStoreState) {
    this.#gtmModule = gtmModule
    this.#accessor = store
    this.#authStoreState = authStoreState
  }

  /**
   * Push many events to dataLayer.
   * @param events GTM dataLayer event.
   */
  gtmPushMany(...events: any[]) {
    for (let i = 0; i < events?.length; i++) {
      this.gtmPush(events[i])
    }
  }

  /**
   * Push event to dataLayer. Optional callback with timeout can be specified. Callback is called once the push finishes.
   * @param event GTM's dataLayer event.
   * @param callback Callback when push is finished.
   * @param timeout Push callback timeout.
   */
  gtmPush(event: any, callback: EventCallback = null, timeout: number = 500) {
    if (!event) {
      return
    }

    const dlEvent = event as IDataLayerEventV2

    if (callback) {
      // The event callback is debounced, in case there are multiple GTM containers.
      dlEvent.eventCallback = debounce(callback, 200)
      dlEvent.eventTimeout = timeout
    }

    this._dlPushSafe(event)
  }

  pushPageView(options: { isLandingPage: boolean }) {
    const loggedIn = this.#authStoreState.loggedIn ?? false
    const browserId = this.#accessor.client.browserId ?? ''
    const pagesubtype = options.isLandingPage ? 'landing-page' : 'standard-page'
    const user = this.#authStoreState.user ?? <ClientData>{}
    const address = this.#accessor.client.clientAddresses[0] ?? <Address>{}
    const siteInfo = this.#accessor.siteInfo ?? <SiteInfo>{}

    const pageViewEvent = createPageViewEvent(
      loggedIn,
      browserId,
      'magazine',
      pagesubtype,
      '',
      user,
      address,
      siteInfo,
    ) as IPageViewEvent

    this.gtmPush(pageViewEvent)
  }

  trackPromoImpressions(articles: IMagazinPromo[]) {
    this.gtmPush({
      event: 'promotionImpression',
      ecommerce: {
        promoView: {
          promotions: articles,
        },
      },
    })
  }

  trackPromoClicks(articles: IMagazinPromo[]) {
    this.gtmPush({
      event: 'promotionClick',
      ecommerce: {
        promoClick: {
          promotions: articles,
        },
      },
    })
  }

  trackProductImpressions(products: IProductPromo[]) {
    this.gtmPush({
      event: 'productImpression',
      ecommerce: {
        currencyCode: this.#accessor.siteInfo.currency,
        impressions: products,
      },
    })
  }

  trackProductClick(products: IProductPromo[]) {
    this.gtmPush({
      event: 'productClick',
      ecommerce: {
        click: {
          products,
        },
      },
    })
  }

  /** Pushes ecommerce clear event, requested by the GTM/Exponea team. */
  pushClearEventsBeforeBasketChange() {
    this.gtmPushClearKey('ecommerce')
    // this.gtmPushClearKey('products');
  }

  /**
   * Clear the GTM event by its key. Pushes to DL this object: { `key`: null }
   * @param key Key to be cleared.
   */
  gtmPushClearKey(key: string) {
    const clearEvent: any = {}
    clearEvent[key] = null

    this._dlPushSafe(clearEvent)
  }

  /**
   * Safe push to GTM dataLayer.
   * @param event Event to be pushed to GTM's dataLayer.
   */
  private _dlPushSafe(event: any) {
    if (!event) {
      return
    }

    if (!this.#gtmModule) {
      if (typeof event.eventCallback === 'function') {
        event.eventCallback()
      }
      return
    }

    this.#gtmModule.push(event)
  }
}

export default function ({ app }: any, inject: (key: string, value: Function | object) => void) {
  inject('ecommerce', new EcommercePlugin(app.$gtm, app.$accessor, app.store.state.auth))
}
