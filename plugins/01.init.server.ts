export default defineNuxtPlugin({
  name: 'nuxt-initialize',

  async setup() {
    try {
      const { setSiteInfo, checkJwt, getUserDefinedAppScripts, getAuthMe } = useStoreAuth()
      const { setBasketCommodities, setBasketFavorites } = useStoreBasket()

      const response = await useAuthorization()
      if (response instanceof Error || response === false) {
        throw response
      }

      setSiteInfo(response)
      await checkJwt()
      await getUserDefinedAppScripts()
      await getAuthMe()
      await setBasketCommodities()
      await setBasketFavorites()
    }
    catch (error) {
      console.error(error)
      throw new Error(error instanceof Error ? error.message : String(error))
    }
  },
})
