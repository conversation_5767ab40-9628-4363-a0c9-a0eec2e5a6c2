import uuid from '@/node_modules/@atx-eshop/eshop-components/dist/runtime/utils/uuid'

export default defineNuxtPlugin({
  name: 'rewriterApi',

  async setup() {
    const config = useRuntimeConfig().public

    const rewriterApi = $fetch.create({
      baseURL: config.magazineURL,
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-store',
        'X-Gateway-Key': config.gatewayKey,
      },

      onRequest({ options }) {
        const siteApi = useSiteLocation() // na ka<PERSON>d<PERSON> request znovu
        const headers = useRequestHeaders(['cookie', 'x-forwarded-for'])
        const traceId = uuid()
        const token = useBearer()

        // options.baseURL = siteApi.url.eshop
        options.headers.set('X-Localization', siteApi.localization)
        options.headers.set('X-Application', config.applicationType as string)
        options.headers.set('X-TraceId', traceId)
        options.headers.set('Authorization', token)
        options.headers.set('Referer', useRoute().fullPath)

        if (headers.cookie) {
          options.headers.set('cookie', headers.cookie)
        }

        if (import.meta.server) {
          options.headers.set('X-Forwarded-For', headers['x-forwarded-for'] || '')
        }
      },
    })

    return {
      provide: {
        rewriterApi,
      },
    }
  },
})
